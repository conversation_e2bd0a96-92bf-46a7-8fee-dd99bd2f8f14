#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文书生成工具GUI界面
功能：提供谷歌风格的用户界面，支持Excel文件上传、预览和Word文档生成
实现逻辑：使用tkinter创建现代化界面，集成文件处理功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import pandas as pd
from pathlib import Path

class DocumentGeneratorGUI:
    def __init__(self, root):
        """
        初始化GUI界面

        Args:
            root: tkinter根窗口
        """
        self.root = root
        self.root.title("文书生成工具 v1.0")

        # 设置高DPI支持
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

        # 获取屏幕尺寸并设置窗口大小
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = min(1600, int(screen_width * 0.9))
        window_height = min(1000, int(screen_height * 0.9))

        # 居中显示窗口
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.configure(bg='#f8f9fa')
        self.root.minsize(1400, 900)

        # 设置窗口状态为最大化
        self.root.state('zoomed')

        # 设置窗口图标和样式
        self.setup_styles()
        
        # 存储上传的Excel文件路径
        self.excel_file_path = None
        self.excel_data = None
        
        # 创建主界面
        self.create_main_interface()
        
    def setup_styles(self):
        """设置现代化的样式"""
        style = ttk.Style()

        # 设置主题
        style.theme_use('clam')

        # 配置主按钮样式
        style.configure('Primary.TButton',
                       background='#1976d2',
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(25, 12),
                       font=('Microsoft YaHei UI', 11, 'bold'))

        style.map('Primary.TButton',
                 background=[('active', '#1565c0'),
                           ('pressed', '#0d47a1')])

        # 配置次要按钮样式
        style.configure('Secondary.TButton',
                       background='#ffffff',
                       foreground='#1976d2',
                       borderwidth=1,
                       focuscolor='none',
                       padding=(20, 10),
                       font=('Microsoft YaHei UI', 10))

        style.map('Secondary.TButton',
                 background=[('active', '#f5f5f5'),
                           ('pressed', '#e0e0e0')])

        # 配置标签样式
        style.configure('Title.TLabel',
                       background='#f8f9fa',
                       foreground='#1a1a1a',
                       font=('Microsoft YaHei UI', 28, 'bold'))

        style.configure('Subtitle.TLabel',
                       background='#f8f9fa',
                       foreground='#666666',
                       font=('Microsoft YaHei UI', 14))

        style.configure('Info.TLabel',
                       background='#f8f9fa',
                       foreground='#333333',
                       font=('Microsoft YaHei UI', 11))

        style.configure('Success.TLabel',
                       background='#f8f9fa',
                       foreground='#2e7d32',
                       font=('Microsoft YaHei UI', 11, 'bold'))

        # 配置框架样式
        style.configure('Card.TLabelframe',
                       background='#ffffff',
                       borderwidth=1,
                       relief='solid')

        style.configure('Card.TLabelframe.Label',
                       background='#ffffff',
                       foreground='#1976d2',
                       font=('Microsoft YaHei UI', 13, 'bold'))

        # 配置Treeview样式
        style.configure('Modern.Treeview',
                       background='#ffffff',
                       foreground='#333333',
                       fieldbackground='#ffffff',
                       font=('Microsoft YaHei UI', 10))

        style.configure('Modern.Treeview.Heading',
                       background='#f5f5f5',
                       foreground='#1976d2',
                       font=('Microsoft YaHei UI', 11, 'bold'))
        
    def create_main_interface(self):
        """创建主界面"""
        # 主容器
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=50, pady=40)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg='#f8f9fa')
        title_frame.pack(fill='x', pady=(0, 40))

        title_label = ttk.Label(title_frame, text="文书生成工具", style='Title.TLabel')
        title_label.pack()

        subtitle_label = ttk.Label(title_frame,
                                  text="智能化文书生成系统 - 支持多人员信息批量处理",
                                  style='Subtitle.TLabel')
        subtitle_label.pack(pady=(15, 0))

        # 功能区域
        self.create_upload_section(main_frame)
        self.create_preview_section(main_frame)
        self.create_generate_section(main_frame)
        
    def create_upload_section(self, parent):
        """创建文件上传区域"""
        upload_frame = ttk.LabelFrame(parent, text="步骤1: 上传Excel文件",
                                    style='Card.TLabelframe',
                                    padding=(30, 20))
        upload_frame.pack(fill='x', pady=(0, 25))

        # 上传按钮和状态
        button_frame = tk.Frame(upload_frame, bg='#ffffff')
        button_frame.pack(fill='x')

        self.upload_btn = ttk.Button(button_frame, text="📁 选择Excel文件",
                                   style='Primary.TButton',
                                   command=self.upload_excel_file)
        self.upload_btn.pack(side='left')

        self.file_status_label = ttk.Label(button_frame, text="未选择文件",
                                         style='Info.TLabel')
        self.file_status_label.pack(side='left', padx=(25, 0))

        # 模板下载链接
        template_frame = tk.Frame(upload_frame, bg='#ffffff')
        template_frame.pack(fill='x', pady=(15, 0))

        template_label = ttk.Label(template_frame, text="💡 没有模板？", style='Info.TLabel')
        template_label.pack(side='left')

        self.download_template_btn = ttk.Button(template_frame, text="📥 下载Excel模板",
                                              style='Secondary.TButton',
                                              command=self.download_template)
        self.download_template_btn.pack(side='left', padx=(15, 0))
        
    def create_preview_section(self, parent):
        """创建数据预览区域"""
        preview_frame = ttk.LabelFrame(parent, text="步骤2: 预览数据",
                                     style='Card.TLabelframe',
                                     padding=(30, 20))
        preview_frame.pack(fill='both', expand=True, pady=(0, 25))

        # 创建表格
        self.create_data_table(preview_frame)
        
    def create_data_table(self, parent):
        """创建数据表格"""
        # 表格框架
        table_frame = tk.Frame(parent, bg='#ffffff')
        table_frame.pack(fill='both', expand=True)

        # 创建Treeview表格
        columns = ['序号', '被谈话人姓名', '身份证号', '单位及职务', '谈话人1', '谈话人2', '谈话人3', '案件名称']
        self.data_tree = ttk.Treeview(table_frame, columns=columns, show='headings',
                                    height=12, style='Modern.Treeview')

        # 设置列标题和宽度
        for col in columns:
            self.data_tree.heading(col, text=col)
            if col == '序号':
                self.data_tree.column(col, width=60, anchor='center')
            elif col == '被谈话人姓名':
                self.data_tree.column(col, width=100, anchor='center')
            elif col == '身份证号':
                self.data_tree.column(col, width=150, anchor='center')
            elif col in ['谈话人1', '谈话人2', '谈话人3']:
                self.data_tree.column(col, width=100, anchor='center')
            elif col == '单位及职务':
                self.data_tree.column(col, width=250, anchor='w')
            else:
                self.data_tree.column(col, width=200, anchor='w')

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.data_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
    def create_generate_section(self, parent):
        """创建文档生成区域"""
        generate_frame = ttk.LabelFrame(parent, text="步骤3: 生成文书",
                                      style='Card.TLabelframe',
                                      padding=(30, 20))
        generate_frame.pack(fill='x')

        # 生成选项
        options_frame = tk.Frame(generate_frame, bg='#ffffff')
        options_frame.pack(fill='x', pady=(0, 20))

        ttk.Label(options_frame, text="📋 选择要生成的文书类型:", style='Info.TLabel').pack(side='left')

        self.doc_types = {
            "询问呈批表及通知书": tk.BooleanVar(value=True),
            "走读式谈话审批表格": tk.BooleanVar(value=True),
            "封面": tk.BooleanVar(value=True),
            "集体研判记录": tk.BooleanVar(value=True),
            "证人权利义务告知书": tk.BooleanVar(value=True)
        }

        checkbox_frame = tk.Frame(generate_frame, bg='#ffffff')
        checkbox_frame.pack(fill='x', pady=(0, 20))

        for i, (doc_type, var) in enumerate(self.doc_types.items()):
            cb = ttk.Checkbutton(checkbox_frame, text=f"📄 {doc_type}", variable=var,
                               style='Modern.TCheckbutton')
            cb.grid(row=i//3, column=i%3, sticky='w', padx=(0, 30), pady=5)

        # 生成按钮
        button_frame = tk.Frame(generate_frame, bg='#ffffff')
        button_frame.pack(fill='x')

        self.generate_btn = ttk.Button(button_frame, text="🚀 开始生成文书",
                                     style='Primary.TButton',
                                     command=self.generate_documents,
                                     state='disabled')
        self.generate_btn.pack(side='left')

        self.progress_label = ttk.Label(button_frame, text="", style='Success.TLabel')
        self.progress_label.pack(side='left', padx=(25, 0))
        
    def upload_excel_file(self):
        """上传Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )
        
        if file_path:
            try:
                # 读取Excel文件
                self.excel_data = pd.read_excel(file_path)
                self.excel_file_path = file_path
                
                # 更新状态
                filename = os.path.basename(file_path)
                self.file_status_label.config(text=f"已选择: {filename}")
                
                # 更新预览表格
                self.update_preview_table()
                
                # 启用生成按钮
                self.generate_btn.config(state='normal')
                
                messagebox.showinfo("成功", f"Excel文件加载成功！\n共{len(self.excel_data)}条记录")
                
            except Exception as e:
                messagebox.showerror("错误", f"读取Excel文件失败：\n{str(e)}")
                
    def update_preview_table(self):
        """更新预览表格"""
        # 清空现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)

        if self.excel_data is not None:
            # 显示前10行数据
            display_data = self.excel_data.head(10)

            for index, row in display_data.iterrows():
                values = [
                    row.get('序号', index + 1),
                    row.get('被谈话人姓名', ''),
                    row.get('被谈话人身份证号', ''),
                    row.get('被谈话人单位及职务', ''),
                    row.get('谈话人1姓名', ''),
                    row.get('谈话人2姓名', ''),
                    row.get('谈话人3姓名', ''),
                    row.get('案件名称', '')
                ]
                self.data_tree.insert('', 'end', values=values)
                
    def download_template(self):
        """下载Excel模板"""
        try:
            # 检查模板文件是否存在
            template_path = "人员信息模板.xlsx"
            if not os.path.exists(template_path):
                # 如果不存在，创建模板
                from create_excel_template import create_excel_template
                create_excel_template()

            # 让用户选择保存位置，设置默认文件名
            save_path = filedialog.asksaveasfilename(
                title="保存Excel模板",
                defaultextension=".xlsx",
                initialfile="人员信息模板.xlsx",
                filetypes=[("Excel文件", "*.xlsx")]
            )

            if save_path:
                # 检查是否是同一个文件
                if os.path.abspath(template_path) == os.path.abspath(save_path):
                    messagebox.showinfo("提示", "模板文件已存在于当前目录，无需重复下载")
                    return

                import shutil
                shutil.copy2(template_path, save_path)
                messagebox.showinfo("成功", f"模板已保存到：\n{save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"下载模板失败：\n{str(e)}")
            
    def generate_documents(self):
        """生成文书"""
        if self.excel_data is None:
            messagebox.showwarning("警告", "请先上传Excel文件")
            return

        # 获取选中的文书类型
        selected_types = [doc_type for doc_type, var in self.doc_types.items() if var.get()]

        if not selected_types:
            messagebox.showwarning("警告", "请至少选择一种文书类型")
            return

        # 选择保存路径
        output_dir = filedialog.askdirectory(
            title="选择文书保存目录",
            initialdir=os.path.expanduser("~/Desktop")  # 默认桌面
        )

        if not output_dir:
            return  # 用户取消了选择

        try:
            # 导入文档处理模块
            from document_processor import DocumentProcessor

            self.progress_label.config(text="正在生成文书...")
            self.generate_btn.config(state='disabled')
            self.root.update()

            # 创建文档处理器
            processor = DocumentProcessor()

            # 定义进度回调函数
            def progress_callback(progress, message):
                self.progress_label.config(text=f"进度: {progress:.1f}% - {message}")
                self.root.update()

            # 生成文书
            results = processor.generate_documents(
                self.excel_file_path,
                selected_types,
                output_dir,
                progress_callback
            )

            # 显示结果
            if results["success"]:
                success_msg = f"文书生成完成！\n"
                success_msg += f"生成类型：{', '.join(selected_types)}\n"
                success_msg += f"生成文件数：{len(results['generated_files'])}\n"
                success_msg += f"生成文件夹数：{len(results['output_folders'])}\n"
                success_msg += f"保存位置：{output_dir}"

                if results["errors"]:
                    success_msg += f"\n\n注意：有 {len(results['errors'])} 个错误"

                messagebox.showinfo("成功", success_msg)
                self.progress_label.config(text="生成完成")

                # 询问是否打开输出目录
                if messagebox.askyesno("打开目录", "是否打开生成文书的目录？"):
                    import subprocess
                    subprocess.Popen(f'explorer "{output_dir}"', shell=True)
            else:
                error_msg = "文书生成失败！\n\n错误信息：\n"
                error_msg += "\n".join(results["errors"][:5])  # 只显示前5个错误
                if len(results["errors"]) > 5:
                    error_msg += f"\n... 还有 {len(results['errors']) - 5} 个错误"

                messagebox.showerror("错误", error_msg)
                self.progress_label.config(text="生成失败")

        except Exception as e:
            messagebox.showerror("错误", f"生成文书失败：\n{str(e)}")
            self.progress_label.config(text="生成失败")
        finally:
            self.generate_btn.config(state='normal')

def main():
    """主函数"""
    root = tk.Tk()
    app = DocumentGeneratorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
