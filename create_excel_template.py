#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel模板创建工具
功能：创建用于填写人员信息的Excel模板
实现逻辑：使用openpyxl库创建Excel文件，定义各种字段的列结构
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter

def create_excel_template():
    """
    创建Excel模板文件
    
    Returns:
        str: 创建的Excel文件路径
    """
    # 创建工作簿
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "人员信息模板"
    
    # 定义表头和字段
    headers = [
        "序号",
        # 被谈话人信息
        "被谈话人姓名",
        "被谈话人性别",
        "被谈话人年龄",
        "被谈话人民族",
        "被谈话人学历",
        "被谈话人政治面貌",
        "被谈话人单位及职务",
        "被谈话人身份证号",
        "被谈话人联系电话",
        "被谈话人家庭住址",
        "被谈话人工作单位地址",
        # 谈话人信息
        "谈话人1姓名",
        "谈话人1单位及职务",
        "谈话人2姓名",
        "谈话人2单位及职务",
        # 安全员信息
        "安全员姓名",
        "安全员单位及职务",
        # 组长信息
        "组长姓名",
        "组长单位及职务",
        # 其他信息
        "案件名称",
        "填报部门",
        "填报日期",
        "询问地点",
        "备注"
    ]
    
    # 设置表头样式
    header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center')
    
    # 写入表头
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment
        
        # 设置列宽
        column_letter = get_column_letter(col)
        if header in ['序号']:
            ws.column_dimensions[column_letter].width = 8
        elif '姓名' in header or header in ['性别', '年龄', '民族', '学历']:
            ws.column_dimensions[column_letter].width = 12
        elif header in ['政治面貌', '填报部门', '询问地点'] or '政治面貌' in header:
            ws.column_dimensions[column_letter].width = 15
        elif '单位及职务' in header or header in ['案件名称', '家庭住址', '工作单位地址']:
            ws.column_dimensions[column_letter].width = 25
        elif '身份证号' in header or '联系电话' in header:
            ws.column_dimensions[column_letter].width = 18
        else:
            ws.column_dimensions[column_letter].width = 12
    
    # 添加示例数据行
    sample_data = [
        [1,
         # 被谈话人信息
         "张三", "男", "45岁", "汉族", "本科", "中共党员",
         "某市检察院党组成员、副检察长", "123456789012345678", "13800138000",
         "某市某区某街道123号", "某市检察院",
         # 谈话人信息
         "王五", "某市纪委监委第一监督检查室主任",
         "赵六", "某市纪委监委第一监督检查室副主任",
         # 安全员信息
         "孙七", "某市纪委监委安全保卫处干部",
         # 组长信息
         "周八", "某市纪委监委副主任",
         # 其他信息
         "李某涉嫌违纪违法及职务犯罪案", "第四监督检查室", "2025年7月31日",
         "某市监委", "示例数据"],
        [2,
         # 被谈话人信息
         "李四", "女", "38岁", "汉族", "硕士", "中共党员",
         "某县法院审判委员会委员、副院长", "987654321098765432", "13900139000",
         "某县某镇某村456号", "某县法院",
         # 谈话人信息
         "陈九", "某县纪委监委第二监督检查室主任",
         "刘十", "某县纪委监委第二监督检查室副主任",
         # 安全员信息
         "吴一", "某县纪委监委安全保卫处干部",
         # 组长信息
         "郑二", "某县纪委监委副主任",
         # 其他信息
         "王某涉嫌贪污受贿案", "第三监督检查室", "2025年8月1日",
         "某县监委", "示例数据"]
    ]
    
    # 设置数据行样式
    data_font = Font(name='微软雅黑', size=11)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)
    
    # 写入示例数据
    for row_idx, row_data in enumerate(sample_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws.cell(row=row_idx, column=col_idx, value=value)
            cell.font = data_font
            cell.alignment = data_alignment
    
    # 设置行高
    ws.row_dimensions[1].height = 25  # 表头行高
    for row in range(2, len(sample_data) + 2):
        ws.row_dimensions[row].height = 20  # 数据行高
    
    # 添加数据验证和说明
    ws2 = wb.create_sheet("填写说明")
    
    instructions = [
        ["字段名称", "填写说明", "示例"],
        ["姓名", "被询问人的真实姓名", "张三"],
        ["性别", "男/女", "男"],
        ["年龄", "实际年龄+岁", "45岁"],
        ["民族", "民族名称", "汉族"],
        ["学历", "最高学历", "本科"],
        ["政治面貌", "党员身份", "中共党员"],
        ["单位及职务", "完整的工作单位和职务信息", "某市检察院党组成员、副检察长"],
        ["案件名称", "相关案件的完整名称", "李某涉嫌违纪违法及职务犯罪案"],
        ["填报部门", "负责填报的部门", "第四监督检查室"],
        ["填报日期", "格式：YYYY年MM月DD日", "2025年7月31日"],
        ["询问地点", "进行询问的地点", "某市监委"],
        ["身份证号", "18位身份证号码", "123456789012345678"],
        ["联系电话", "11位手机号码", "13800138000"],
        ["家庭住址", "详细的家庭住址", "某市某区某街道123号"],
        ["工作单位地址", "工作单位的详细地址", "某市检察院"],
        ["备注", "其他需要说明的信息", "可选填写"]
    ]
    
    # 设置说明页样式
    for row_idx, row_data in enumerate(instructions, 1):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws2.cell(row=row_idx, column=col_idx, value=value)
            if row_idx == 1:  # 表头
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            else:
                cell.font = data_font
                cell.alignment = data_alignment
    
    # 设置说明页列宽
    ws2.column_dimensions['A'].width = 15
    ws2.column_dimensions['B'].width = 30
    ws2.column_dimensions['C'].width = 25
    
    # 保存文件
    template_path = "人员信息模板.xlsx"
    wb.save(template_path)
    
    print(f"Excel模板已创建: {template_path}")
    print(f"包含工作表: {', '.join(wb.sheetnames)}")
    
    return template_path

if __name__ == "__main__":
    create_excel_template()
