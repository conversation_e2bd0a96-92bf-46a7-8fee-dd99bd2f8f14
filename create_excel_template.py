#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel模板创建工具
功能：创建用于填写人员信息的Excel模板
实现逻辑：使用openpyxl库创建Excel文件，定义各种字段的列结构
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.datavalidation import DataValidation

def setup_worksheet_headers(ws, headers, header_font, header_fill, header_alignment):
    """设置工作表表头"""
    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=1, column=col, value=header)
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = header_alignment

        # 设置列宽
        column_letter = get_column_letter(col)
        if header in ['序号']:
            ws.column_dimensions[column_letter].width = 8
        elif '姓名' in header or header in ['民族', '学历']:
            ws.column_dimensions[column_letter].width = 12
        elif header in ['政治面貌', '填报部门', '询问地点', '谈话类型', '审批层次'] or '政治面貌' in header:
            ws.column_dimensions[column_letter].width = 15
        elif '单位及职务' in header or header in ['案件名称', '家庭住址', '工作单位地址']:
            ws.column_dimensions[column_letter].width = 25
        elif '身份证号' in header:
            ws.column_dimensions[column_letter].width = 20
        elif '联系电话' in header or '日期' in header:
            ws.column_dimensions[column_letter].width = 15
        else:
            ws.column_dimensions[column_letter].width = 12

def add_data_validation(ws, cell_range, validation_list):
    """添加数据验证下拉列表"""
    dv = DataValidation(type="list", formula1=f'"{",".join(validation_list)}"')
    dv.error = '请从下拉列表中选择'
    dv.errorTitle = '输入错误'
    ws.add_data_validation(dv)
    dv.add(cell_range)

def create_excel_template():
    """
    创建Excel模板文件

    Returns:
        str: 创建的Excel文件路径
    """
    # 创建工作簿
    wb = openpyxl.Workbook()

    # 创建被谈话人信息工作表
    ws1 = wb.active
    ws1.title = "被谈话人信息"

    # 被谈话人信息表头
    interviewee_headers = [
        "序号",
        "被谈话人姓名",
        "被谈话人身份证类型",
        "被谈话人身份证号",
        "被谈话人民族",
        "被谈话人籍贯",
        "被谈话人学历",
        "被谈话人政治面貌",
        "被谈话人单位",
        "被谈话人职务",
        "被谈话人联系电话",
        "被谈话人家庭住址",
        "被谈话人工作单位地址",
        "案件名称",
        "填报部门",
        "填报日期",
        "询问地点",
        "谈话日期",
        "谈话类型",
        "谈话地点",
        "审批层次",
        "批准人",
        "备注"
    ]

    # 创建谈话人信息工作表
    ws2 = wb.create_sheet("谈话人信息")

    # 谈话人信息表头
    interviewer_headers = [
        "序号",
        "关联被谈话人姓名",
        "谈话人姓名",
        "谈话人单位及职务",
        "谈话人职责",
        "谈话时间",
        "研判地点",
        "研判日期",
        "备注"
    ]
    
    # 设置表头样式
    header_font = Font(name='微软雅黑', size=12, bold=True, color='FFFFFF')
    header_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
    header_alignment = Alignment(horizontal='center', vertical='center')

    # 设置被谈话人信息表头
    setup_worksheet_headers(ws1, interviewee_headers, header_font, header_fill, header_alignment)

    # 设置谈话人信息表头
    setup_worksheet_headers(ws2, interviewer_headers, header_font, header_fill, header_alignment)

    # 设置数据行样式
    data_font = Font(name='微软雅黑', size=11)
    data_alignment = Alignment(horizontal='left', vertical='center', wrap_text=True)

    # 添加被谈话人示例数据
    interviewee_sample_data = [
        [1, "张三", "身份证", "123456197801234567", "汉族", "贵州贵阳", "本科", "中共党员",
         "某市检察院", "党组成员、副检察长", "13800138000",
         "某市某区某街道123号", "某市检察院",
         "李某涉嫌违纪违法及职务犯罪案", "第四监督检查室", "2025年7月31日",
         "某市监委", "2025-08-02", "3类谈话", "某市监委谈话室", "其他人员", "王主任", "示例数据"],
        [2, "李四", "身份证", "987654198505678901", "汉族", "贵州遵义", "硕士", "中共党员",
         "某县法院", "审判委员会委员、副院长", "13900139000",
         "某县某镇某村456号", "某县法院",
         "王某涉嫌贪污受贿案", "第三监督检查室", "2025年8月1日",
         "某县监委", "2025-08-03", "监督类谈话", "某县监委谈话室", "省管副职", "李局长", "示例数据"]
    ]

    # 添加谈话人示例数据
    interviewer_sample_data = [
        [1, "张三", "王五", "某市纪委监委第一监督检查室主任", "组长", "2025年8月2日上午9:00", "某市监委会议室", "2025-08-01", "示例数据"],
        [2, "张三", "赵六", "某市纪委监委第一监督检查室副主任", "负责人", "2025年8月2日上午9:00", "某市监委会议室", "2025-08-01", "示例数据"],
        [3, "张三", "钱七", "某市纪委监委第一监督检查室干部", "谈话人", "2025年8月2日上午9:00", "某市监委会议室", "2025-08-01", "示例数据"],
        [4, "张三", "孙七", "某市纪委监委安全保卫处干部", "安全员", "2025年8月2日上午9:00", "某市监委会议室", "2025-08-01", "示例数据"],
        [5, "李四", "陈九", "某县纪委监委第二监督检查室主任", "组长", "2025年8月3日下午2:00", "某县监委谈话室", "2025-08-02", "示例数据"],
        [6, "李四", "刘十", "某县纪委监委第二监督检查室副主任", "负责人", "2025年8月3日下午2:00", "某县监委谈话室", "2025-08-02", "示例数据"],
        [7, "李四", "孙八", "某县纪委监委第二监督检查室干部", "谈话人", "2025年8月3日下午2:00", "某县监委谈话室", "2025-08-02", "示例数据"],
        [8, "李四", "李九", "某县纪委监委第二监督检查室干部", "安全员", "2025年8月3日下午2:00", "某县监委谈话室", "2025-08-02", "示例数据"]
    ]

    # 写入被谈话人示例数据
    for row_idx, row_data in enumerate(interviewee_sample_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws1.cell(row=row_idx, column=col_idx, value=value)
            cell.font = data_font
            cell.alignment = data_alignment

    # 写入谈话人示例数据
    for row_idx, row_data in enumerate(interviewer_sample_data, 2):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws2.cell(row=row_idx, column=col_idx, value=value)
            cell.font = data_font
            cell.alignment = data_alignment

    # 设置行高
    ws1.row_dimensions[1].height = 25  # 表头行高
    ws2.row_dimensions[1].height = 25  # 表头行高
    for row in range(2, len(interviewee_sample_data) + 2):
        ws1.row_dimensions[row].height = 20  # 数据行高
    for row in range(2, len(interviewer_sample_data) + 2):
        ws2.row_dimensions[row].height = 20  # 数据行高

    # 添加数据验证下拉列表
    # 谈话类型下拉列表（增加监督类谈话）
    talk_types = ["1类谈话", "2类谈话", "3类谈话", "监督类谈话"]
    add_data_validation(ws1, "S3:S1000", talk_types)  # S列是谈话类型列

    # 审批层次下拉列表
    approval_levels = ["省管正职", "省管副职", "省管其他干部", "其他人员"]
    add_data_validation(ws1, "U3:U1000", approval_levels)  # U列是审批层次列

    # 谈话人职责下拉列表
    responsibilities = ["组长", "负责人", "谈话人", "安全员"]
    add_data_validation(ws2, "E3:E1000", responsibilities)  # E列是谈话人职责列

    # 添加填写说明工作表
    ws3 = wb.create_sheet("填写说明")

    instructions = [
        ["工作表", "字段名称", "填写说明", "示例"],
        ["被谈话人信息", "被谈话人姓名", "被询问人的真实姓名", "张三"],
        ["被谈话人信息", "被谈话人身份证号", "18位身份证号码（自动提取性别年龄）", "123456197801234567"],
        ["被谈话人信息", "被谈话人民族", "民族名称", "汉族"],
        ["被谈话人信息", "被谈话人学历", "最高学历", "本科"],
        ["被谈话人信息", "被谈话人政治面貌", "党员身份", "中共党员"],
        ["被谈话人信息", "被谈话人单位及职务", "完整的工作单位和职务信息", "某市检察院党组成员、副检察长"],
        ["被谈话人信息", "案件名称", "相关案件的完整名称", "李某涉嫌违纪违法及职务犯罪案"],
        ["被谈话人信息", "填报部门", "负责填报的部门", "第四监督检查室"],
        ["被谈话人信息", "填报日期", "格式：YYYY年MM月DD日", "2025年7月31日"],
        ["被谈话人信息", "谈话日期", "格式：YYYY-MM-DD", "2025-08-02"],
        ["被谈话人信息", "谈话类型", "从下拉列表选择", "3类谈话"],
        ["被谈话人信息", "谈话地点", "具体谈话地点", "某市监委谈话室"],
        ["被谈话人信息", "审批层次", "从下拉列表选择", "其他人员"],
        ["谈话人信息", "关联被谈话人姓名", "对应被谈话人的姓名", "张三"],
        ["谈话人信息", "谈话人1-4姓名", "参与谈话的人员姓名", "王五"],
        ["谈话人信息", "谈话人1-4单位及职务", "谈话人的职务信息", "某市纪委监委主任"],
        ["谈话人信息", "安全员姓名", "安全监督员姓名", "孙七"],
        ["谈话人信息", "组长姓名", "谈话组组长姓名", "周八"],
        ["谈话人信息", "谈话时间", "具体谈话时间", "2025年8月2日上午9:00"],
        ["谈话人信息", "研判地点", "集体研判地点", "某市监委会议室"]
    ]

    # 设置说明页样式
    for row_idx, row_data in enumerate(instructions, 1):
        for col_idx, value in enumerate(row_data, 1):
            cell = ws3.cell(row=row_idx, column=col_idx, value=value)
            if row_idx == 1:  # 表头
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            else:
                cell.font = data_font
                cell.alignment = data_alignment

    # 设置说明页列宽
    ws3.column_dimensions['A'].width = 15
    ws3.column_dimensions['B'].width = 20
    ws3.column_dimensions['C'].width = 30
    ws3.column_dimensions['D'].width = 25
    
    # 保存文件
    template_path = "人员信息模板.xlsx"
    wb.save(template_path)
    
    print(f"Excel模板已创建: {template_path}")
    print(f"包含工作表: {', '.join(wb.sheetnames)}")
    
    return template_path

if __name__ == "__main__":
    create_excel_template()
