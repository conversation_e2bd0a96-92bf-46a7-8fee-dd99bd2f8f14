#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Word模板中的字段
"""

from docx import Document
import re

def analyze_template(template_path):
    """分析Word模板中的占位符"""
    print(f"\n=== 分析模板: {template_path} ===")
    
    try:
        doc = Document(template_path)
        
        # 收集所有文本
        all_text = []
        
        # 段落文本
        for para in doc.paragraphs:
            if para.text.strip():
                all_text.append(para.text)
        
        # 表格文本
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for para in cell.paragraphs:
                        if para.text.strip():
                            all_text.append(para.text)
        
        # 查找占位符
        placeholders = set()
        for text in all_text:
            # 查找所有占位符模式
            matches = re.findall(r'[{【]([^}】]+)[}】]', text)
            placeholders.update(matches)
        
        # 查找包含"填报日期"的文本
        date_texts = []
        for text in all_text:
            if "填报日期" in text or "日期" in text:
                date_texts.append(text)
        
        print("找到的占位符:")
        for placeholder in sorted(placeholders):
            print(f"  - {placeholder}")
        
        print("\n包含日期的文本:")
        for text in date_texts:
            print(f"  - {text}")
            
        return placeholders, date_texts
        
    except Exception as e:
        print(f"分析失败: {e}")
        return set(), []

def main():
    templates = [
        "询问/询问呈批表及通知书.docx",
        "询问/整合后走读式谈话审批表格.docx",
        "询问/封面.docx",
        "询问/集体研判记录.docx",
        "询问/证人权利义务告知书.docx"
    ]
    
    for template in templates:
        analyze_template(template)

if __name__ == "__main__":
    main()
