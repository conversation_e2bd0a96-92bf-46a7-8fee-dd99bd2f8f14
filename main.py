#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文书生成工具主程序
功能：启动文书生成工具的图形界面
实现逻辑：导入GUI模块并启动主界面
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = {
        'pandas': 'pandas',
        'openpyxl': 'openpyxl',
        'python-docx': 'docx'
    }
    missing_packages = []

    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        error_msg = f"缺少以下依赖包：\n{', '.join(missing_packages)}\n\n"
        error_msg += "请运行以下命令安装：\n"
        error_msg += f"pip install {' '.join(missing_packages)}"
        
        # 创建一个简单的错误窗口
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("依赖包缺失", error_msg)
        return False
    
    return True

def check_templates():
    """检查模板文件是否存在"""
    template_dir = "询问"
    required_templates = [
        "询问呈批表及通知书.docx",
        "整合后走读式谈话审批表格.docx",
        "封面.docx",
        "集体研判记录.docx",
        "证人权利义务告知书.docx"
    ]

    missing_templates = []

    if not os.path.exists(template_dir):
        return False, f"模板目录不存在：{template_dir}"

    # 获取目录中实际存在的文件列表
    try:
        actual_files = os.listdir(template_dir)
        actual_files = [f for f in actual_files if f.endswith('.docx')]
    except Exception as e:
        return False, f"无法读取模板目录：{str(e)}"

    for template in required_templates:
        template_path = os.path.join(template_dir, template)
        if not os.path.exists(template_path):
            missing_templates.append(template)

    if missing_templates:
        error_msg = f"缺少以下模板文件：\n"
        for template in missing_templates:
            error_msg += f"  - {template}\n"
        error_msg += f"\n请确保这些文件存在于 {template_dir} 目录中"
        error_msg += f"\n\n当前目录中的文件：\n"
        for file in actual_files:
            error_msg += f"  - {file}\n"
        return False, error_msg

    return True, ""

def main():
    """主函数"""
    print("正在启动文书生成工具...")
    
    # 检查依赖包
    if not check_dependencies():
        print("依赖包检查失败，程序退出")
        return
    
    # 检查模板文件
    templates_ok, error_msg = check_templates()
    if not templates_ok:
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("模板文件缺失", error_msg)
        print(f"模板文件检查失败：{error_msg}")
        return
    
    try:
        # 导入并启动GUI
        from document_generator_gui import DocumentGeneratorGUI
        
        root = tk.Tk()
        app = DocumentGeneratorGUI(root)
        
        print("文书生成工具已启动")
        root.mainloop()
        
    except Exception as e:
        error_msg = f"启动程序失败：\n{str(e)}"
        print(error_msg)
        
        # 显示错误对话框
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("启动失败", error_msg)

if __name__ == "__main__":
    main()
