#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档处理模块
功能：读取Excel数据，替换Word文档中的字段，生成新的文书
实现逻辑：使用python-docx处理Word文档，openpyxl处理Excel文件，实现字段映射和替换
"""

import os
import pandas as pd
from docx import Document
from pathlib import Path
import re
from datetime import datetime

class DocumentProcessor:
    def __init__(self):
        """初始化文档处理器"""
        self.template_dir = "询问"
        self.output_dir = "生成的文书"
        
        # 文档模板映射
        self.template_mapping = {
            "询问呈批表及通知书": "询问呈批表及通知书.docx",
            "走读式谈话审批表格": '整合后"走读式"谈话审批表格.docx',
            "封面": "1.封面.docx",
            "目录": "2.目录.docx",
            "集体研判记录": "3.集体研判记录.docx",
            "证人权利义务告知书": "证人权利义务告知书.docx"
        }
        
        # 字段映射关系（Excel列名 -> Word文档中的占位符）
        self.field_mapping = {
            "姓名": ["岑小明", "谭虎", "赵孝祥", "龚世平", "杨盛富"],
            "性别": ["男", "女"],
            "年龄": ["54岁", "45岁", "38岁"],
            "民族": ["汉族"],
            "学历": ["本科", "硕士", "大专"],
            "政治面貌": ["中共党员", "群众"],
            "单位及职务": ["黔南州检察院党组成员、副检察长"],
            "案件名称": ["黄桂林涉嫌违纪违法及职务犯罪案", "黄桂林涉嫌违纪违法和职务犯罪案"],
            "填报部门": ["第四监督检查室", "第三监督检查室"],
            "填报日期": ["2025年7月31日", "2025年8月1日"],
            "询问地点": ["某市监委", "某县监委"]
        }
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def read_excel_data(self, excel_path):
        """
        读取Excel文件数据
        
        Args:
            excel_path (str): Excel文件路径
            
        Returns:
            pandas.DataFrame: Excel数据
        """
        try:
            df = pd.read_excel(excel_path)
            return df
        except Exception as e:
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    def replace_text_in_paragraph(self, paragraph, replacements):
        """
        替换段落中的文本
        
        Args:
            paragraph: Word段落对象
            replacements (dict): 替换映射字典
        """
        # 获取段落的完整文本
        full_text = paragraph.text
        
        # 执行替换
        for old_text, new_text in replacements.items():
            if old_text in full_text:
                full_text = full_text.replace(old_text, str(new_text))
        
        # 如果文本发生了变化，更新段落
        if full_text != paragraph.text:
            # 清空段落内容
            paragraph.clear()
            # 添加新文本
            paragraph.add_run(full_text)
    
    def replace_text_in_table(self, table, replacements):
        """
        替换表格中的文本
        
        Args:
            table: Word表格对象
            replacements (dict): 替换映射字典
        """
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    self.replace_text_in_paragraph(paragraph, replacements)
    
    def create_replacements_dict(self, person_data):
        """
        根据人员数据创建替换字典
        
        Args:
            person_data (dict): 人员数据字典
            
        Returns:
            dict: 替换映射字典
        """
        replacements = {}
        
        # 基于字段映射创建替换字典
        for field, excel_value in person_data.items():
            if field in self.field_mapping and pd.notna(excel_value):
                # 将Excel中的值替换文档中对应的占位符
                for placeholder in self.field_mapping[field]:
                    replacements[placeholder] = excel_value
        
        # 添加一些特殊的替换规则
        if "姓名" in person_data and pd.notna(person_data["姓名"]):
            name = person_data["姓名"]
            # 替换所有可能的姓名占位符
            for placeholder_name in ["岑小明", "谭虎", "赵孝祥", "龚世平", "杨盛富"]:
                replacements[placeholder_name] = name
        
        if "案件名称" in person_data and pd.notna(person_data["案件名称"]):
            case_name = person_data["案件名称"]
            # 替换所有可能的案件名称占位符
            for placeholder_case in ["黄桂林涉嫌违纪违法及职务犯罪案", "黄桂林涉嫌违纪违法和职务犯罪案"]:
                replacements[placeholder_case] = case_name
        
        return replacements
    
    def process_document(self, template_path, output_path, person_data):
        """
        处理单个文档
        
        Args:
            template_path (str): 模板文档路径
            output_path (str): 输出文档路径
            person_data (dict): 人员数据
        """
        try:
            # 打开模板文档
            doc = Document(template_path)
            
            # 创建替换字典
            replacements = self.create_replacements_dict(person_data)
            
            # 替换段落中的文本
            for paragraph in doc.paragraphs:
                self.replace_text_in_paragraph(paragraph, replacements)
            
            # 替换表格中的文本
            for table in doc.tables:
                self.replace_text_in_table(table, replacements)
            
            # 保存文档
            doc.save(output_path)
            
        except Exception as e:
            raise Exception(f"处理文档失败: {str(e)}")
    
    def generate_documents(self, excel_path, selected_types, progress_callback=None):
        """
        生成文书
        
        Args:
            excel_path (str): Excel文件路径
            selected_types (list): 选中的文书类型列表
            progress_callback (function): 进度回调函数
            
        Returns:
            dict: 生成结果
        """
        try:
            # 读取Excel数据
            df = self.read_excel_data(excel_path)
            
            results = {
                "success": True,
                "generated_files": [],
                "errors": []
            }
            
            total_tasks = len(df) * len(selected_types)
            current_task = 0
            
            # 为每个人员生成文书
            for index, row in df.iterrows():
                person_name = row.get("姓名", f"人员{index+1}")
                
                # 为每种选中的文书类型生成文档
                for doc_type in selected_types:
                    try:
                        if doc_type not in self.template_mapping:
                            results["errors"].append(f"未找到文书类型: {doc_type}")
                            continue
                        
                        template_file = self.template_mapping[doc_type]
                        template_path = os.path.join(self.template_dir, template_file)
                        
                        if not os.path.exists(template_path):
                            results["errors"].append(f"模板文件不存在: {template_path}")
                            continue
                        
                        # 生成输出文件名
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        output_filename = f"{person_name}_{doc_type}_{timestamp}.docx"
                        output_path = os.path.join(self.output_dir, output_filename)
                        
                        # 处理文档
                        self.process_document(template_path, output_path, row.to_dict())
                        
                        results["generated_files"].append(output_path)
                        
                        # 更新进度
                        current_task += 1
                        if progress_callback:
                            progress = (current_task / total_tasks) * 100
                            progress_callback(progress, f"已生成: {output_filename}")
                        
                    except Exception as e:
                        error_msg = f"生成 {person_name} 的 {doc_type} 失败: {str(e)}"
                        results["errors"].append(error_msg)
            
            return results
            
        except Exception as e:
            return {
                "success": False,
                "generated_files": [],
                "errors": [f"生成文书失败: {str(e)}"]
            }

def main():
    """测试函数"""
    processor = DocumentProcessor()
    
    # 测试Excel文件路径
    excel_path = "人员信息模板.xlsx"
    
    if os.path.exists(excel_path):
        selected_types = ["询问呈批表及通知书", "证人权利义务告知书"]
        
        def progress_callback(progress, message):
            print(f"进度: {progress:.1f}% - {message}")
        
        results = processor.generate_documents(excel_path, selected_types, progress_callback)
        
        print(f"\n生成结果:")
        print(f"成功: {results['success']}")
        print(f"生成文件数: {len(results['generated_files'])}")
        
        if results['generated_files']:
            print("生成的文件:")
            for file_path in results['generated_files']:
                print(f"  - {file_path}")
        
        if results['errors']:
            print("错误信息:")
            for error in results['errors']:
                print(f"  - {error}")
    else:
        print(f"Excel文件不存在: {excel_path}")

if __name__ == "__main__":
    main()
