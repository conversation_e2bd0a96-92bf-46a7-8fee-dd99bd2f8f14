#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档处理模块
功能：读取Excel数据，替换Word文档中的字段，生成新的文书
实现逻辑：使用python-docx处理Word文档，openpyxl处理Excel文件，实现字段映射和替换
"""

import os
import pandas as pd
from docx import Document
from pathlib import Path
import re
from datetime import datetime, date

class DocumentProcessor:
    def __init__(self):
        """初始化文档处理器"""
        self.template_dir = "询问"
        self.output_dir = "生成的文书"
        
        # 文档模板映射
        self.template_mapping = {
            "询问呈批表及通知书": "询问呈批表及通知书.docx",
            "走读式谈话审批表格": "整合后走读式谈话审批表格.docx",
            "封面": "封面.docx",
            "集体研判记录": "集体研判记录.docx",
            "证人权利义务告知书": "证人权利义务告知书.docx"
        }
        
        # 由于您已经修改了模板文件使用占位符，我们现在直接使用占位符替换
        # 不再需要复杂的字段映射，直接根据占位符名称替换
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

    def format_date_chinese(self, date_str):
        """
        将日期格式转换为中文格式

        Args:
            date_str: 日期字符串，支持多种格式

        Returns:
            str: 中文格式的日期，如"2025年8月1日"
        """
        if not date_str or pd.isna(date_str):
            return ""

        try:
            date_str = str(date_str).strip()

            # 尝试解析不同的日期格式
            if '-' in date_str:
                # 格式：2025-08-01 或 2025-8-1
                parts = date_str.split('-')
                if len(parts) == 3:
                    year = int(parts[0])
                    month = int(parts[1])
                    day = int(parts[2])
                    return f"{year}年{month}月{day}日"
            elif '年' in date_str and '月' in date_str and '日' in date_str:
                # 已经是中文格式
                return date_str
            elif len(date_str) == 8 and date_str.isdigit():
                # 格式：20250801
                year = int(date_str[:4])
                month = int(date_str[4:6])
                day = int(date_str[6:8])
                return f"{year}年{month}月{day}日"

            return date_str
        except:
            return str(date_str)

    def extract_info_from_id_card(self, id_card):
        """
        从身份证号提取性别和年龄信息

        Args:
            id_card (str): 身份证号码

        Returns:
            dict: 包含性别和年龄的字典
        """
        if not id_card:
            return {"性别": "", "年龄": ""}

        try:
            id_str = str(id_card).strip()

            # 检查身份证号长度
            if len(id_str) != 18:
                return {"性别": "", "年龄": ""}

            # 提取出生日期
            birth_year = int(id_str[6:10])
            birth_month = int(id_str[10:12])
            birth_day = int(id_str[12:14])

            # 验证日期有效性
            if birth_year < 1900 or birth_year > 2100:
                return {"性别": "", "年龄": ""}
            if birth_month < 1 or birth_month > 12:
                return {"性别": "", "年龄": ""}
            if birth_day < 1 or birth_day > 31:
                return {"性别": "", "年龄": ""}

            # 计算年龄
            today = date.today()
            birth_date = date(birth_year, birth_month, birth_day)
            age = today.year - birth_date.year
            if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
                age -= 1

            # 提取性别（倒数第二位，奇数为男，偶数为女）
            gender_digit = int(id_str[16])
            gender = "男" if gender_digit % 2 == 1 else "女"

            return {"性别": gender, "年龄": f"{age}岁"}

        except Exception as e:
            print(f"提取身份证信息失败: {str(e)}")
            return {"性别": "", "年龄": ""}
    
    def read_excel_data(self, excel_path):
        """
        读取Excel文件数据（支持多工作表）

        Args:
            excel_path (str): Excel文件路径

        Returns:
            list: 包含每个被谈话人完整信息的列表
        """
        try:
            # 读取所有工作表
            excel_data = pd.read_excel(excel_path, sheet_name=None)

            # 处理新的分离式工作表结构
            if "被谈话人信息" in excel_data and "谈话人信息" in excel_data:
                interviewee_df = excel_data["被谈话人信息"]
                interviewer_df = excel_data["谈话人信息"]

                # 为每个被谈话人组织完整的数据
                complete_data = []

                for _, interviewee_row in interviewee_df.iterrows():
                    person_name = interviewee_row["被谈话人姓名"]

                    # 获取该被谈话人的所有谈话人信息
                    person_interviewers = interviewer_df[
                        interviewer_df["关联被谈话人姓名"] == person_name
                    ]

                    # 组织数据结构
                    person_data = interviewee_row.to_dict()

                    # 按职责分类谈话人
                    for _, interviewer_row in person_interviewers.iterrows():
                        role = interviewer_row["谈话人职责"]
                        name = interviewer_row["谈话人姓名"]
                        position = interviewer_row["谈话人单位及职务"]

                        if role == "组长":
                            person_data["组长姓名"] = name
                            person_data["组长单位及职务"] = position
                        elif role == "负责人":
                            person_data["负责人姓名"] = name
                            person_data["负责人单位及职务"] = position
                        elif role == "安全员":
                            person_data["安全员姓名"] = name
                            person_data["安全员单位及职务"] = position
                        elif role == "谈话人":
                            # 为谈话人编号
                            for i in range(1, 10):  # 支持最多9个谈话人
                                if f"谈话人{i}姓名" not in person_data or pd.isna(person_data.get(f"谈话人{i}姓名")):
                                    person_data[f"谈话人{i}姓名"] = name
                                    person_data[f"谈话人{i}单位及职务"] = position
                                    break

                    # 添加谈话时间和研判地点（从第一个谈话人记录中获取）
                    if not person_interviewers.empty:
                        first_interviewer = person_interviewers.iloc[0]
                        person_data["谈话时间"] = first_interviewer.get("谈话时间", "")
                        person_data["研判地点"] = first_interviewer.get("研判地点", "")
                        person_data["研判日期"] = first_interviewer.get("研判日期", "")

                    complete_data.append(person_data)

                return complete_data
            else:
                # 如果是旧格式，直接返回第一个工作表
                return list(excel_data.values())[0].to_dict('records')

        except Exception as e:
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    def replace_text_in_paragraph(self, paragraph, replacements):
        """
        替换段落中的文本，保持原有格式但移除红色字体

        Args:
            paragraph: Word段落对象
            replacements (dict): 替换映射字典
        """
        # 遍历段落中的所有runs
        for run in paragraph.runs:
            original_text = run.text
            new_text = original_text

            # 执行替换
            for old_text, replacement in replacements.items():
                if old_text in new_text:
                    new_text = new_text.replace(old_text, str(replacement))

            # 如果文本发生了变化，更新run的文本（保持格式但移除红色）
            if new_text != original_text:
                run.text = new_text
                # 移除红色字体，设置为黑色
                if run.font.color.rgb:
                    from docx.shared import RGBColor
                    run.font.color.rgb = RGBColor(0, 0, 0)  # 设置为黑色
    
    def replace_text_in_table(self, table, replacements):
        """
        替换表格中的文本，保持原有格式

        Args:
            table: Word表格对象
            replacements (dict): 替换映射字典
        """
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    self.replace_text_in_paragraph(paragraph, replacements)
    
    def create_replacements_dict(self, person_data):
        """
        根据人员数据创建替换字典（使用占位符直接替换）

        Args:
            person_data (dict): 人员数据字典

        Returns:
            dict: 替换映射字典
        """
        replacements = {}

        # 从身份证号提取性别和年龄
        if "被谈话人身份证号" in person_data and pd.notna(person_data["被谈话人身份证号"]):
            id_info = self.extract_info_from_id_card(person_data["被谈话人身份证号"])
            replacements["被谈话人性别"] = id_info["性别"]
            replacements["被谈话人年龄"] = id_info["年龄"]

        # 直接使用占位符替换
        field_mappings = {
            # 被谈话人基本信息
            "被谈话人姓名": "被谈话人姓名",
            "被谈话人身份证类型": "被谈话人身份证类型",
            "被谈话人身份证号": "被谈话人身份证号",
            "被谈话人民族": "被谈话人民族",
            "被谈话人籍贯": "被谈话人籍贯",
            "被谈话人学历": "被谈话人学历",
            "被谈话人政治面貌": "被谈话人政治面貌",
            "被谈话人单位": "被谈话人单位",
            "被谈话人职务": "被谈话人职务",
            "被谈话人联系电话": "被谈话人联系电话",
            "被谈话人家庭住址": "被谈话人家庭住址",
            "被谈话人工作单位地址": "被谈话人工作单位地址",
            # 案件信息
            "案件名称": "案件名称",
            "填报部门": "填报部门",
            "填报日期": "填报日期",
            "询问地点": "询问地点",
            "谈话日期": "谈话日期",
            "谈话类型": "谈话类型",
            "谈话地点": "谈话地点",
            "审批层次": "审批层次",
            "批准人": "批准人",
            # 谈话人信息
            "组长姓名": "组长姓名",
            "组长单位及职务": "组长单位及职务",
            "负责人姓名": "负责人姓名",
            "负责人单位及职务": "负责人单位及职务",
            "安全员姓名": "安全员姓名",
            "安全员单位及职务": "安全员单位及职务",
            "谈话时间": "谈话时间",
            "研判地点": "研判地点",
            "研判日期": "研判日期"
        }

        # 添加谈话人信息（支持多个谈话人）
        for i in range(1, 10):
            field_mappings[f"谈话人{i}姓名"] = f"谈话人{i}姓名"
            field_mappings[f"谈话人{i}单位及职务"] = f"谈话人{i}单位及职务"

        # 执行字段映射
        for excel_field, placeholder in field_mappings.items():
            if excel_field in person_data and pd.notna(person_data[excel_field]):
                value = person_data[excel_field]

                # 特殊处理日期格式
                if '日期' in excel_field or '时间' in excel_field:
                    if excel_field in ['填报日期', '谈话日期', '研判日期']:
                        value = self.format_date_chinese(value)

                replacements[placeholder] = str(value)
        
        # 处理审批层次的勾选（根据Excel数据自动勾选对应选项）
        if "审批层次" in person_data and pd.notna(person_data["审批层次"]):
            approval_level = str(person_data["审批层次"])
            # 设置所有选项为未选中状态，然后勾选对应项
            replacements["省管正职选择"] = "√" if "省管正职" in approval_level else "  "
            replacements["省管副职选择"] = "√" if "省管副职" in approval_level else "  "
            replacements["省管其他干部选择"] = "√" if "省管其他干部" in approval_level else "  "
            replacements["其他人员选择"] = "√" if "其他人员" in approval_level else "  "

        # 处理身份证件信息组合
        if "被谈话人身份证类型" in person_data and "被谈话人身份证号" in person_data:
            id_type = person_data.get("被谈话人身份证类型", "身份证")
            id_number = person_data.get("被谈话人身份证号", "")
            if pd.notna(id_type) and pd.notna(id_number):
                replacements["身份证件及号码"] = f"{id_type} {id_number}"

        # 处理单位职务组合
        if "被谈话人单位" in person_data and "被谈话人职务" in person_data:
            unit = person_data.get("被谈话人单位", "")
            position = person_data.get("被谈话人职务", "")
            if pd.notna(unit) and pd.notna(position):
                replacements["被谈话人单位及职务"] = f"{unit}{position}"

        # 处理联系人（询问通知书用）
        contact_persons = []
        for i in range(1, 10):
            interviewer_key = f"谈话人{i}姓名"
            if interviewer_key in person_data and pd.notna(person_data[interviewer_key]):
                contact_persons.append(person_data[interviewer_key])

        if contact_persons:
            # 如果只有一个谈话人，只显示一个
            if len(contact_persons) == 1:
                replacements["联系人"] = contact_persons[0]
                # 清空第二个谈话人的占位符
                replacements["谈话人2姓名"] = ""
            else:
                # 多个谈话人用顿号连接
                replacements["联系人"] = "、".join(contact_persons[:2])  # 最多显示两个

        # 处理集体研判记录的完整内容
        self.process_group_discussion_record(person_data, replacements)

        return replacements

    def process_group_discussion_record(self, person_data, replacements):
        """处理集体研判记录的内容"""
        if "被谈话人姓名" not in person_data or pd.isna(person_data["被谈话人姓名"]):
            return

        name = person_data["被谈话人姓名"]
        talk_type = person_data.get("谈话类型", "监督类谈话")
        talk_location = person_data.get("谈话地点", "某县监委谈话室")
        case_name = person_data.get("案件名称", "")

        # 获取所有参与人员
        leader = person_data.get("组长姓名", "")
        responsible = person_data.get("负责人姓名", "")
        safety_officer = person_data.get("安全员姓名", "")

        # 获取所有谈话人
        interviewers = []
        for i in range(1, 10):
            interviewer_key = f"谈话人{i}姓名"
            if interviewer_key in person_data and pd.notna(person_data[interviewer_key]):
                interviewers.append(person_data[interviewer_key])

        # 组织研讨人员列表（去除空值）
        discussion_members = []
        if leader: discussion_members.append(leader)
        if responsible: discussion_members.append(responsible)
        discussion_members.extend(interviewers)
        if safety_officer: discussion_members.append(safety_officer)

        # 去重并保持顺序
        seen = set()
        unique_members = []
        for member in discussion_members:
            if member not in seen:
                unique_members.append(member)
                seen.add(member)

        # 替换研讨人员
        replacements["研讨人员"] = "、".join(unique_members)

        # 替换对话内容
        if leader:
            replacements["组长请承办人汇报"] = f"{leader}：请承办人汇报一下相关情况。"
            replacements["组长请大家发表意见"] = f"{leader}：请大家发表意见。"

            # 组长总结发言
            leader_summary = f"{leader}：同意在{talk_location}对{name}开展{talk_type}，要严格按照办案纪律进行，重要情况及时报告，注意办案安全。"
            replacements["组长总结发言"] = leader_summary

        # 负责人汇报
        if responsible:
            team_members = []
            if leader: team_members.append(leader)
            team_members.extend(interviewers)

            report_text = f"{responsible}：根据工作需要，结合前期谈话情况，拟对{name}谈话，建议在{talk_location}开展并适用{talk_type}。"
            if leader:
                report_text += f"谈话组由{leader}同志任组长，"
            if team_members:
                report_text += "、".join(team_members) + "任组员，"
            if safety_officer:
                report_text += f"{safety_officer}任安全监督员。"
            report_text += "谈话情况详见走读式谈话方案。"

            replacements["负责人汇报"] = report_text

        # 谈话人发言
        for i, interviewer in enumerate(interviewers):
            if i == 0 and not responsible:  # 如果没有负责人，第一个谈话人做汇报
                team_members = []
                if leader: team_members.append(leader)
                team_members.extend(interviewers)

                report_text = f"{interviewer}：根据工作需要，结合前期谈话情况，拟对{name}谈话，建议在{talk_location}开展并适用{talk_type}。"
                if leader:
                    report_text += f"谈话组由{leader}同志任组长，"
                if team_members:
                    report_text += "、".join(team_members) + "任组员，"
                if safety_officer:
                    report_text += f"{safety_officer}任安全监督员。"
                report_text += "谈话情况详见走读式谈话方案。"

                replacements[f"谈话人{i+1}发言"] = report_text
            else:
                # 其他谈话人的简单表态
                statement = f"{interviewer}：同意在{talk_location}对{name}开展{talk_type}。"
                replacements[f"谈话人{i+1}发言"] = statement

        # 清空多余的谈话人发言
        for i in range(len(interviewers), 10):
            replacements[f"谈话人{i+1}发言"] = ""

        # 议定内容
        议定内容 = f"议  定：在{talk_location}对{name}开展{talk_type}，严格执行谈话方案和安全预案，确保办案安全。"
        replacements["议定内容"] = 议定内容
    
    def process_document(self, template_path, output_path, person_data):
        """
        处理单个文档
        
        Args:
            template_path (str): 模板文档路径
            output_path (str): 输出文档路径
            person_data (dict): 人员数据
        """
        try:
            # 打开模板文档
            doc = Document(template_path)
            
            # 创建替换字典
            replacements = self.create_replacements_dict(person_data)
            
            # 替换段落中的文本
            for paragraph in doc.paragraphs:
                self.replace_text_in_paragraph(paragraph, replacements)
            
            # 替换表格中的文本
            for table in doc.tables:
                self.replace_text_in_table(table, replacements)
            
            # 保存文档
            doc.save(output_path)
            
        except Exception as e:
            raise Exception(f"处理文档失败: {str(e)}")
    
    def generate_documents(self, excel_path, selected_types, output_base_dir, progress_callback=None):
        """
        生成文书

        Args:
            excel_path (str): Excel文件路径
            selected_types (list): 选中的文书类型列表
            output_base_dir (str): 输出基础目录
            progress_callback (function): 进度回调函数

        Returns:
            dict: 生成结果
        """
        try:
            # 读取Excel数据（现在返回的是列表）
            data_list = self.read_excel_data(excel_path)

            results = {
                "success": True,
                "generated_files": [],
                "errors": [],
                "output_folders": []
            }

            total_tasks = len(data_list) * len(selected_types)
            current_task = 0

            # 获取当前日期
            current_date = datetime.now().strftime("%Y%m%d")

            # 为每个人员生成文书
            for index, person_data in enumerate(data_list):
                person_name = person_data.get("被谈话人姓名", f"人员{index+1}")

                # 创建个人文件夹：日期+姓名
                person_folder_name = f"{current_date}_{person_name}"
                person_folder_path = os.path.join(output_base_dir, person_folder_name)
                os.makedirs(person_folder_path, exist_ok=True)

                if person_folder_path not in results["output_folders"]:
                    results["output_folders"].append(person_folder_path)

                # 为每种选中的文书类型生成文档
                for doc_type in selected_types:
                    try:
                        if doc_type not in self.template_mapping:
                            results["errors"].append(f"未找到文书类型: {doc_type}")
                            continue

                        template_file = self.template_mapping[doc_type]
                        template_path = os.path.join(self.template_dir, template_file)

                        if not os.path.exists(template_path):
                            results["errors"].append(f"模板文件不存在: {template_path}")
                            continue

                        # 生成输出文件名：文书名+被谈话人姓名+填报时间
                        fill_date = person_data.get("填报日期", "")
                        if fill_date:
                            # 将日期转换为简洁格式，如"2025年8月1日" -> "20250801"
                            try:
                                if "年" in fill_date and "月" in fill_date and "日" in fill_date:
                                    # 提取年月日数字
                                    import re
                                    numbers = re.findall(r'\d+', fill_date)
                                    if len(numbers) >= 3:
                                        date_suffix = f"{numbers[0]}{numbers[1]:0>2}{numbers[2]:0>2}"
                                    else:
                                        date_suffix = fill_date.replace("年", "").replace("月", "").replace("日", "")
                                else:
                                    date_suffix = fill_date
                            except:
                                date_suffix = fill_date
                        else:
                            date_suffix = ""

                        output_filename = f"{doc_type}_{person_name}_{date_suffix}.docx"
                        output_path = os.path.join(person_folder_path, output_filename)

                        # 处理文档
                        self.process_document(template_path, output_path, person_data)

                        results["generated_files"].append(output_path)

                        # 更新进度
                        current_task += 1
                        if progress_callback:
                            progress = (current_task / total_tasks) * 100
                            progress_callback(progress, f"已生成: {person_name} - {doc_type}")

                    except Exception as e:
                        error_msg = f"生成 {person_name} 的 {doc_type} 失败: {str(e)}"
                        results["errors"].append(error_msg)

            return results

        except Exception as e:
            return {
                "success": False,
                "generated_files": [],
                "errors": [f"生成文书失败: {str(e)}"],
                "output_folders": []
            }

def main():
    """测试函数"""
    processor = DocumentProcessor()
    
    # 测试Excel文件路径
    excel_path = "人员信息模板.xlsx"
    
    if os.path.exists(excel_path):
        selected_types = ["询问呈批表及通知书", "证人权利义务告知书"]
        
        def progress_callback(progress, message):
            print(f"进度: {progress:.1f}% - {message}")
        
        results = processor.generate_documents(excel_path, selected_types, progress_callback)
        
        print(f"\n生成结果:")
        print(f"成功: {results['success']}")
        print(f"生成文件数: {len(results['generated_files'])}")
        
        if results['generated_files']:
            print("生成的文件:")
            for file_path in results['generated_files']:
                print(f"  - {file_path}")
        
        if results['errors']:
            print("错误信息:")
            for error in results['errors']:
                print(f"  - {error}")
    else:
        print(f"Excel文件不存在: {excel_path}")

if __name__ == "__main__":
    main()
