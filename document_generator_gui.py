#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文书生成工具GUI界面
功能：提供谷歌风格的用户界面，支持Excel文件上传、预览和Word文档生成
实现逻辑：使用tkinter创建现代化界面，集成文件处理功能
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import pandas as pd
from pathlib import Path

class DocumentGeneratorGUI:
    def __init__(self, root):
        """
        初始化GUI界面

        Args:
            root: tkinter根窗口
        """
        self.root = root
        self.root.title("文书生成工具 v1.0")

        # 设置高DPI支持
        try:
            from ctypes import windll
            windll.shcore.SetProcessDpiAwareness(1)
        except:
            pass

        # 获取屏幕尺寸并设置窗口大小
        screen_width = root.winfo_screenwidth()
        screen_height = root.winfo_screenheight()
        window_width = min(1600, int(screen_width * 0.9))
        window_height = min(1000, int(screen_height * 0.9))

        # 居中显示窗口
        x = (screen_width - window_width) // 2
        y = (screen_height - window_height) // 2
        self.root.geometry(f"{window_width}x{window_height}+{x}+{y}")
        self.root.configure(bg='#f8f9fa')
        self.root.minsize(1400, 900)

        # 设置窗口状态为最大化
        self.root.state('zoomed')

        # 设置窗口图标和样式
        self.setup_styles()
        
        # 存储上传的Excel文件路径
        self.excel_file_path = None
        self.excel_data = None
        
        # 创建主界面
        self.create_main_interface()
        
    def setup_styles(self):
        """设置谷歌风格的样式"""
        style = ttk.Style()

        # 设置主题
        style.theme_use('clam')

        # 谷歌Material Design颜色
        self.colors = {
            'primary': '#4285F4',      # Google Blue
            'primary_dark': '#3367D6',
            'secondary': '#34A853',    # Google Green
            'secondary_dark': '#137333',
            'accent': '#EA4335',       # Google Red
            'accent_dark': '#D33B2C',
            'warning': '#FBBC04',      # Google Yellow
            'warning_dark': '#F9AB00',
            'surface': '#FFFFFF',
            'background': '#F8F9FA',
            'on_surface': '#202124',
            'on_background': '#5F6368'
        }

        # 配置主按钮样式 - Google Blue
        style.configure('Primary.TButton',
                       background=self.colors['primary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(30, 15),
                       font=('Microsoft YaHei UI', 12, 'bold'))

        style.map('Primary.TButton',
                 background=[('active', self.colors['primary_dark']),
                           ('pressed', '#1A73E8')])

        # 配置成功按钮样式 - Google Green
        style.configure('Success.TButton',
                       background=self.colors['secondary'],
                       foreground='white',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(30, 15),
                       font=('Microsoft YaHei UI', 12, 'bold'))

        style.map('Success.TButton',
                 background=[('active', self.colors['secondary_dark']),
                           ('pressed', '#0F9D58')])

        # 配置警告按钮样式 - Google Yellow
        style.configure('Warning.TButton',
                       background=self.colors['warning'],
                       foreground='#202124',
                       borderwidth=0,
                       focuscolor='none',
                       padding=(25, 12),
                       font=('Microsoft YaHei UI', 11, 'bold'))

        style.map('Warning.TButton',
                 background=[('active', self.colors['warning_dark']),
                           ('pressed', '#F29900')])

        # 配置次要按钮样式
        style.configure('Secondary.TButton',
                       background=self.colors['surface'],
                       foreground=self.colors['primary'],
                       borderwidth=2,
                       focuscolor='none',
                       padding=(25, 12),
                       font=('Microsoft YaHei UI', 11))

        style.map('Secondary.TButton',
                 background=[('active', '#F1F3F4'),
                           ('pressed', '#E8EAED')])

        # 配置标签样式
        style.configure('Title.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['on_surface'],
                       font=('Microsoft YaHei UI', 32, 'bold'))

        style.configure('Subtitle.TLabel',
                       background=self.colors['background'],
                       foreground=self.colors['on_background'],
                       font=('Microsoft YaHei UI', 16))

        style.configure('Info.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['on_surface'],
                       font=('Microsoft YaHei UI', 12))

        style.configure('Success.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['secondary'],
                       font=('Microsoft YaHei UI', 12, 'bold'))

        style.configure('Error.TLabel',
                       background=self.colors['surface'],
                       foreground=self.colors['accent'],
                       font=('Microsoft YaHei UI', 12, 'bold'))

        # 配置框架样式
        style.configure('Card.TLabelframe',
                       background=self.colors['surface'],
                       borderwidth=0,
                       relief='flat')

        style.configure('Card.TLabelframe.Label',
                       background=self.colors['surface'],
                       foreground=self.colors['primary'],
                       font=('Microsoft YaHei UI', 16, 'bold'))

        # 配置复选框样式
        style.configure('Modern.TCheckbutton',
                       background=self.colors['surface'],
                       foreground=self.colors['on_surface'],
                       font=('Microsoft YaHei UI', 11))
        
    def create_main_interface(self):
        """创建谷歌风格的主界面"""
        # 主容器
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill='both', expand=True, padx=60, pady=50)

        # 标题区域
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(fill='x', pady=(0, 50))

        title_label = tk.Label(title_frame,
                              text="📄 文书生成工具",
                              bg=self.colors['background'],
                              fg=self.colors['on_surface'],
                              font=('Microsoft YaHei UI', 32, 'bold'))
        title_label.pack()

        subtitle_label = tk.Label(title_frame,
                                 text="智能化文书生成系统 - 支持多人员信息批量处理",
                                 bg=self.colors['background'],
                                 fg=self.colors['on_background'],
                                 font=('Microsoft YaHei UI', 16))
        subtitle_label.pack(pady=(20, 0))

        # 创建两列布局
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill='both', expand=True)

        # 左列 - 文件上传 (40%宽度)
        left_frame = tk.Frame(content_frame, bg=self.colors['background'])
        left_frame.pack(side='left', fill='both', expand=True, padx=(0, 30))

        # 右列 - 文书生成 (60%宽度)
        right_frame = tk.Frame(content_frame, bg=self.colors['background'])
        right_frame.pack(side='right', fill='both', expand=True, padx=(30, 0))

        # 功能区域
        self.create_upload_section(left_frame)
        self.create_generate_section(right_frame)
        
    def create_upload_section(self, parent):
        """创建谷歌风格的文件上传区域"""
        # 主卡片
        upload_card = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        upload_card.pack(fill='both', expand=True, pady=(0, 30))

        # 添加阴影效果（简化版本）
        # self.add_shadow(upload_card)

        # 卡片内容
        card_content = tk.Frame(upload_card, bg=self.colors['surface'])
        card_content.pack(fill='both', expand=True, padx=40, pady=40)

        # 标题
        title_label = tk.Label(card_content,
                              text="📁 步骤1: 选择Excel文件",
                              bg=self.colors['surface'],
                              fg=self.colors['primary'],
                              font=('Microsoft YaHei UI', 16, 'bold'))
        title_label.pack(anchor='w', pady=(0, 30))

        # 文件上传提示
        upload_hint = tk.Label(card_content,
                              text="📤 点击下方按钮选择Excel文件",
                              bg=self.colors['surface'],
                              fg=self.colors['on_background'],
                              font=('Microsoft YaHei UI', 14))
        upload_hint.pack(pady=(0, 20))

        # 上传按钮
        self.upload_btn = tk.Button(card_content,
                                   text="🔍 浏览文件",
                                   bg=self.colors['primary'],
                                   fg='white',
                                   font=('Microsoft YaHei UI', 12, 'bold'),
                                   relief='flat',
                                   padx=30,
                                   pady=15,
                                   cursor='hand2',
                                   command=self.upload_excel_file)
        self.upload_btn.pack(pady=(0, 20))

        # 添加悬停效果
        self.add_hover_effect(self.upload_btn, self.colors['primary'], self.colors['primary_dark'])

        # 文件状态
        self.file_status_label = tk.Label(card_content,
                                         text="未选择文件",
                                         bg=self.colors['surface'],
                                         fg=self.colors['on_surface'],
                                         font=('Microsoft YaHei UI', 12))
        self.file_status_label.pack(pady=(0, 30))

        # 分割线
        separator = tk.Frame(card_content, height=1, bg='#E8EAED')
        separator.pack(fill='x', pady=(0, 30))

        # 模板下载区域
        template_frame = tk.Frame(card_content, bg=self.colors['surface'])
        template_frame.pack(fill='x')

        template_icon = tk.Label(template_frame,
                                text="💡",
                                bg=self.colors['surface'],
                                fg=self.colors['on_surface'],
                                font=('Segoe UI Emoji', 16))
        template_icon.pack(side='left')

        template_text = tk.Label(template_frame,
                                text="需要Excel模板？",
                                bg=self.colors['surface'],
                                fg=self.colors['on_surface'],
                                font=('Microsoft YaHei UI', 12))
        template_text.pack(side='left', padx=(10, 20))

        self.download_template_btn = tk.Button(template_frame,
                                              text="📥 下载模板",
                                              bg=self.colors['surface'],
                                              fg=self.colors['primary'],
                                              font=('Microsoft YaHei UI', 11),
                                              relief='solid',
                                              bd=2,
                                              padx=25,
                                              pady=12,
                                              cursor='hand2',
                                              command=self.download_template)
        self.download_template_btn.pack(side='right')

        # 添加悬停效果
        self.add_hover_effect(self.download_template_btn, self.colors['surface'], '#F1F3F4')

    def add_shadow(self, widget):
        """为组件添加阴影效果"""
        shadow = tk.Frame(widget.master, bg='#E0E0E0', height=2)
        shadow.place(in_=widget, x=2, y=2, relwidth=1, relheight=1)

    def add_hover_effect(self, button, normal_color, hover_color):
        """为按钮添加悬停效果"""
        def on_enter(event):
            button.config(bg=hover_color)

        def on_leave(event):
            if button['state'] != 'disabled':
                button.config(bg=normal_color)

        button.bind("<Enter>", on_enter)
        button.bind("<Leave>", on_leave)
        

        
    def create_generate_section(self, parent):
        """创建谷歌风格的文档生成区域"""
        # 主卡片
        generate_card = tk.Frame(parent, bg=self.colors['surface'], relief='solid', bd=1)
        generate_card.pack(fill='both', expand=True)

        # 添加阴影效果（简化版本）
        # self.add_shadow(generate_card)

        # 卡片内容
        card_content = tk.Frame(generate_card, bg=self.colors['surface'])
        card_content.pack(fill='both', expand=True, padx=40, pady=40)

        # 标题
        title_label = tk.Label(card_content,
                              text="🚀 步骤2: 生成文书",
                              bg=self.colors['surface'],
                              fg=self.colors['primary'],
                              font=('Microsoft YaHei UI', 16, 'bold'))
        title_label.pack(anchor='w', pady=(0, 30))

        # 文书类型选择区域
        types_frame = tk.Frame(card_content, bg=self.colors['surface'])
        types_frame.pack(fill='x', pady=(0, 30))

        types_title = tk.Label(types_frame,
                              text="📋 选择要生成的文书类型",
                              bg=self.colors['surface'],
                              fg=self.colors['on_surface'],
                              font=('Microsoft YaHei UI', 14, 'bold'))
        types_title.pack(anchor='w', pady=(0, 20))

        # 文书类型复选框 - 使用彩色卡片样式
        self.doc_types = {
            "询问呈批表及通知书": tk.BooleanVar(value=True),
            "走读式谈话审批表格": tk.BooleanVar(value=True),
            "封面": tk.BooleanVar(value=True),
            "集体研判记录": tk.BooleanVar(value=True),
            "证人权利义务告知书": tk.BooleanVar(value=True)
        }

        # 文书类型图标
        doc_icons = {
            "询问呈批表及通知书": "📋",
            "走读式谈话审批表格": "📝",
            "封面": "📄",
            "集体研判记录": "💬",
            "证人权利义务告知书": "⚖️"
        }

        # 简化的复选框区域
        checkbox_container = tk.Frame(card_content, bg=self.colors['surface'])
        checkbox_container.pack(fill='x', pady=(0, 40))

        for doc_type, var in self.doc_types.items():
            cb = tk.Checkbutton(checkbox_container,
                               text=f"{doc_icons.get(doc_type, '📄')} {doc_type}",
                               variable=var,
                               bg=self.colors['surface'],
                               fg=self.colors['on_surface'],
                               font=('Microsoft YaHei UI', 11),
                               relief='flat')
            cb.pack(anchor='w', pady=5)

        # 分割线
        separator = tk.Frame(card_content, height=1, bg='#E8EAED')
        separator.pack(fill='x', pady=(0, 30))

        # 操作按钮区域
        action_frame = tk.Frame(card_content, bg=self.colors['surface'])
        action_frame.pack(fill='x')

        # 生成按钮
        self.generate_btn = tk.Button(action_frame,
                                     text="🚀 开始生成文书",
                                     bg=self.colors['secondary'],
                                     fg='white',
                                     font=('Microsoft YaHei UI', 12, 'bold'),
                                     relief='flat',
                                     padx=30,
                                     pady=15,
                                     cursor='hand2',
                                     state='disabled',
                                     command=self.generate_documents)
        self.generate_btn.pack(side='left')

        # 添加悬停效果
        self.add_hover_effect(self.generate_btn, self.colors['secondary'], self.colors['secondary_dark'])

        # 进度显示
        self.progress_label = tk.Label(action_frame,
                                      text="",
                                      bg=self.colors['surface'],
                                      fg=self.colors['secondary'],
                                      font=('Microsoft YaHei UI', 12, 'bold'))
        self.progress_label.pack(side='left', padx=(30, 0))

        # 全选/取消全选按钮
        select_frame = tk.Frame(card_content, bg=self.colors['surface'])
        select_frame.pack(fill='x', pady=(20, 0))

        select_all_btn = tk.Button(select_frame,
                                   text="✅ 全选",
                                   bg=self.colors['warning'],
                                   fg='#202124',
                                   font=('Microsoft YaHei UI', 11, 'bold'),
                                   relief='flat',
                                   padx=25,
                                   pady=12,
                                   cursor='hand2',
                                   command=self.select_all_docs)
        select_all_btn.pack(side='left', padx=(0, 10))

        deselect_all_btn = tk.Button(select_frame,
                                     text="❌ 取消全选",
                                     bg=self.colors['surface'],
                                     fg=self.colors['primary'],
                                     font=('Microsoft YaHei UI', 11),
                                     relief='solid',
                                     bd=2,
                                     padx=25,
                                     pady=12,
                                     cursor='hand2',
                                     command=self.deselect_all_docs)
        deselect_all_btn.pack(side='left')

        # 添加悬停效果
        self.add_hover_effect(select_all_btn, self.colors['warning'], self.colors['warning_dark'])
        self.add_hover_effect(deselect_all_btn, self.colors['surface'], '#F1F3F4')

    def select_all_docs(self):
        """全选所有文书类型"""
        for var in self.doc_types.values():
            var.set(True)

    def deselect_all_docs(self):
        """取消选择所有文书类型"""
        for var in self.doc_types.values():
            var.set(False)
        
    def upload_excel_file(self):
        """上传Excel文件"""
        file_path = filedialog.askopenfilename(
            title="选择Excel文件",
            filetypes=[("Excel文件", "*.xlsx *.xls"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    messagebox.showerror("错误", "文件不存在，请重新选择")
                    return

                # 读取Excel文件 - 支持多工作表
                try:
                    excel_data = pd.read_excel(file_path, sheet_name=None)

                    # 检查是否包含必要的工作表
                    if "被谈话人信息" in excel_data:
                        # 新格式：多工作表
                        interviewee_count = len(excel_data["被谈话人信息"])
                        self.excel_file_path = file_path

                        # 更新状态显示
                        filename = os.path.basename(file_path)
                        self.file_status_label.config(
                            text=f"✅ {filename} ({interviewee_count}条记录)",
                            foreground=self.colors['secondary']
                        )

                        # 启用生成按钮
                        self.generate_btn.config(state='normal', bg=self.colors['secondary'])

                        messagebox.showinfo("成功",
                                          f"Excel文件加载成功！\n"
                                          f"文件：{filename}\n"
                                          f"被谈话人：{interviewee_count}人\n"
                                          f"工作表：{', '.join(excel_data.keys())}")
                    else:
                        # 旧格式：单工作表
                        first_sheet = list(excel_data.values())[0]
                        record_count = len(first_sheet)
                        self.excel_file_path = file_path

                        # 更新状态显示
                        filename = os.path.basename(file_path)
                        self.file_status_label.config(
                            text=f"✅ {filename} ({record_count}条记录)",
                            foreground=self.colors['secondary']
                        )

                        # 启用生成按钮
                        self.generate_btn.config(state='normal', bg=self.colors['secondary'])

                        messagebox.showinfo("成功", f"Excel文件加载成功！\n共{record_count}条记录")

                except Exception as read_error:
                    messagebox.showerror("错误", f"读取Excel文件失败：\n{str(read_error)}")

            except Exception as e:
                messagebox.showerror("错误", f"处理文件失败：\n{str(e)}")
                self.file_status_label.config(
                    text="❌ 文件加载失败",
                    foreground=self.colors['accent']
                )
                
    def download_template(self):
        """下载Excel模板"""
        try:
            # 检查模板文件是否存在
            template_path = "人员信息模板.xlsx"
            if not os.path.exists(template_path):
                # 如果不存在，创建模板
                from create_excel_template import create_excel_template
                create_excel_template()

            # 让用户选择保存位置，设置默认文件名
            save_path = filedialog.asksaveasfilename(
                title="保存Excel模板",
                defaultextension=".xlsx",
                initialfile="人员信息模板.xlsx",
                filetypes=[("Excel文件", "*.xlsx")]
            )

            if save_path:
                # 检查是否是同一个文件
                if os.path.abspath(template_path) == os.path.abspath(save_path):
                    messagebox.showinfo("提示", "模板文件已存在于当前目录，无需重复下载")
                    return

                import shutil
                shutil.copy2(template_path, save_path)
                messagebox.showinfo("成功", f"模板已保存到：\n{save_path}")

        except Exception as e:
            messagebox.showerror("错误", f"下载模板失败：\n{str(e)}")
            
    def generate_documents(self):
        """生成文书"""
        if not hasattr(self, 'excel_file_path') or not self.excel_file_path:
            messagebox.showwarning("⚠️ 警告", "请先上传Excel文件")
            return

        # 获取选中的文书类型
        selected_types = [doc_type for doc_type, var in self.doc_types.items() if var.get()]

        if not selected_types:
            messagebox.showwarning("⚠️ 警告", "请至少选择一种文书类型")
            return

        # 选择保存路径
        output_dir = filedialog.askdirectory(
            title="选择文书保存目录",
            initialdir=os.path.expanduser("~/Desktop")  # 默认桌面
        )

        if not output_dir:
            return  # 用户取消了选择

        try:
            # 导入文档处理模块
            from document_processor import DocumentProcessor

            # 更新UI状态
            self.progress_label.config(
                text="🔄 正在初始化...",
                foreground=self.colors['primary']
            )
            self.generate_btn.config(state='disabled', text="⏳ 生成中...", bg='#CCCCCC')
            self.root.update()

            # 创建文档处理器
            processor = DocumentProcessor()

            # 定义进度回调函数
            def progress_callback(progress, message):
                self.progress_label.config(
                    text=f"📊 进度: {progress:.1f}% - {message}",
                    foreground=self.colors['primary']
                )
                self.root.update()

            # 生成文书
            results = processor.generate_documents(
                self.excel_file_path,
                selected_types,
                output_dir,
                progress_callback
            )

            # 显示结果
            if results["success"]:
                # 成功消息
                success_msg = f"🎉 文书生成完成！\n\n"
                success_msg += f"📋 生成类型：{', '.join(selected_types)}\n"
                success_msg += f"📄 生成文件：{len(results['generated_files'])} 个\n"
                success_msg += f"📁 生成文件夹：{len(results['output_folders'])} 个\n"
                success_msg += f"💾 保存位置：{output_dir}"

                if results["errors"]:
                    success_msg += f"\n\n⚠️ 注意：有 {len(results['errors'])} 个警告"

                messagebox.showinfo("✅ 生成成功", success_msg)
                self.progress_label.config(
                    text="✅ 生成完成！",
                    foreground=self.colors['secondary']
                )

                # 询问是否打开输出目录
                if messagebox.askyesno("📂 打开目录", "是否打开生成文书的目录？"):
                    import subprocess
                    subprocess.Popen(f'explorer "{output_dir}"', shell=True)
            else:
                # 错误消息
                error_msg = "❌ 文书生成失败！\n\n错误信息：\n"
                error_msg += "\n".join(results["errors"][:5])  # 只显示前5个错误
                if len(results["errors"]) > 5:
                    error_msg += f"\n... 还有 {len(results['errors']) - 5} 个错误"

                messagebox.showerror("❌ 生成失败", error_msg)
                self.progress_label.config(
                    text="❌ 生成失败",
                    foreground=self.colors['accent']
                )

        except Exception as e:
            messagebox.showerror("❌ 错误", f"生成文书失败：\n{str(e)}")
            self.progress_label.config(
                text="❌ 生成失败",
                foreground=self.colors['accent']
            )
        finally:
            self.generate_btn.config(state='normal', text="🚀 开始生成文书", bg=self.colors['secondary'])

def main():
    """主函数"""
    root = tk.Tk()
    DocumentGeneratorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
