#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档处理模块
功能：读取Excel数据，替换Word文档中的字段，生成新的文书
实现逻辑：使用python-docx处理Word文档，openpyxl处理Excel文件，实现字段映射和替换
"""

import os
import pandas as pd
from docx import Document
from pathlib import Path
import re
from datetime import datetime, date

class DocumentProcessor:
    def __init__(self):
        """初始化文档处理器"""
        self.template_dir = "询问"
        self.output_dir = "生成的文书"
        
        # 文档模板映射
        self.template_mapping = {
            "询问呈批表及通知书": "询问呈批表及通知书.docx",
            "走读式谈话审批表格": "整合后走读式谈话审批表格.docx",
            "封面": "封面.docx",
            "集体研判记录": "集体研判记录.docx",
            "证人权利义务告知书": "证人权利义务告知书.docx"
        }
        
        # 字段映射关系（Excel列名 -> Word文档中的占位符）
        self.field_mapping = {
            # 被谈话人信息
            "被谈话人姓名": ["岑小明", "谭虎", "赵孝祥", "龚世平", "杨盛富"],
            "被谈话人民族": ["汉族"],
            "被谈话人学历": ["本科", "硕士", "大专"],
            "被谈话人政治面貌": ["中共党员", "群众"],
            "被谈话人单位及职务": ["黔南州检察院党组成员、副检察长"],
            "被谈话人身份证号": ["123456789012345678"],
            "被谈话人联系电话": ["13800138000"],
            "被谈话人家庭住址": ["某市某区某街道123号"],
            "被谈话人工作单位地址": ["某市检察院"],
            # 谈话人信息
            "谈话人1姓名": ["王五", "陈九"],
            "谈话人1单位及职务": ["某市纪委监委第一监督检查室主任"],
            "谈话人2姓名": ["赵六", "刘十"],
            "谈话人2单位及职务": ["某市纪委监委第一监督检查室副主任"],
            # 安全员信息
            "安全员姓名": ["孙七", "吴一"],
            "安全员单位及职务": ["某市纪委监委安全保卫处干部"],
            # 组长信息
            "组长姓名": ["周八", "郑二"],
            "组长单位及职务": ["某市纪委监委副主任"],
            # 其他信息
            "案件名称": ["黄桂林涉嫌违纪违法及职务犯罪案", "黄桂林涉嫌违纪违法和职务犯罪案"],
            "填报部门": ["第四监督检查室", "第三监督检查室"],
            "填报日期": ["2025年7月31日", "2025年8月1日"],
            "询问地点": ["某市监委", "某县监委"]
        }
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

    def extract_info_from_id_card(self, id_card):
        """
        从身份证号提取性别和年龄信息

        Args:
            id_card (str): 身份证号码

        Returns:
            dict: 包含性别和年龄的字典
        """
        if not id_card or len(str(id_card)) != 18:
            return {"性别": "", "年龄": ""}

        try:
            id_str = str(id_card)

            # 提取出生日期
            birth_year = int(id_str[6:10])
            birth_month = int(id_str[10:12])
            birth_day = int(id_str[12:14])

            # 计算年龄
            today = date.today()
            birth_date = date(birth_year, birth_month, birth_day)
            age = today.year - birth_date.year
            if today.month < birth_date.month or (today.month == birth_date.month and today.day < birth_date.day):
                age -= 1

            # 提取性别（倒数第二位，奇数为男，偶数为女）
            gender_digit = int(id_str[16])
            gender = "男" if gender_digit % 2 == 1 else "女"

            return {"性别": gender, "年龄": f"{age}岁"}

        except:
            return {"性别": "", "年龄": ""}
    
    def read_excel_data(self, excel_path):
        """
        读取Excel文件数据
        
        Args:
            excel_path (str): Excel文件路径
            
        Returns:
            pandas.DataFrame: Excel数据
        """
        try:
            df = pd.read_excel(excel_path)
            return df
        except Exception as e:
            raise Exception(f"读取Excel文件失败: {str(e)}")
    
    def replace_text_in_paragraph(self, paragraph, replacements):
        """
        替换段落中的文本，保持原有格式

        Args:
            paragraph: Word段落对象
            replacements (dict): 替换映射字典
        """
        # 遍历段落中的所有runs
        for run in paragraph.runs:
            original_text = run.text
            new_text = original_text

            # 执行替换
            for old_text, replacement in replacements.items():
                if old_text in new_text:
                    new_text = new_text.replace(old_text, str(replacement))

            # 如果文本发生了变化，更新run的文本（保持格式）
            if new_text != original_text:
                run.text = new_text
    
    def replace_text_in_table(self, table, replacements):
        """
        替换表格中的文本，保持原有格式

        Args:
            table: Word表格对象
            replacements (dict): 替换映射字典
        """
        for row in table.rows:
            for cell in row.cells:
                for paragraph in cell.paragraphs:
                    self.replace_text_in_paragraph(paragraph, replacements)
    
    def create_replacements_dict(self, person_data):
        """
        根据人员数据创建替换字典

        Args:
            person_data (dict): 人员数据字典

        Returns:
            dict: 替换映射字典
        """
        replacements = {}

        # 从身份证号提取性别和年龄
        if "被谈话人身份证号" in person_data and pd.notna(person_data["被谈话人身份证号"]):
            id_info = self.extract_info_from_id_card(person_data["被谈话人身份证号"])
            # 添加提取的性别和年龄到替换字典
            if id_info["性别"]:
                for placeholder in ["男", "女"]:
                    if placeholder == id_info["性别"]:
                        replacements[placeholder] = id_info["性别"]
            if id_info["年龄"]:
                for placeholder in ["54岁", "45岁", "38岁"]:
                    replacements[placeholder] = id_info["年龄"]

        # 基于字段映射创建替换字典
        for field, excel_value in person_data.items():
            if field in self.field_mapping and pd.notna(excel_value):
                # 将Excel中的值替换文档中对应的占位符
                for placeholder in self.field_mapping[field]:
                    replacements[placeholder] = excel_value
        
        # 添加一些特殊的替换规则
        if "被谈话人姓名" in person_data and pd.notna(person_data["被谈话人姓名"]):
            name = person_data["被谈话人姓名"]
            # 替换所有可能的姓名占位符
            for placeholder_name in ["岑小明", "谭虎", "赵孝祥", "龚世平", "杨盛富"]:
                replacements[placeholder_name] = name

        if "案件名称" in person_data and pd.notna(person_data["案件名称"]):
            case_name = person_data["案件名称"]
            # 替换所有可能的案件名称占位符
            for placeholder_case in ["黄桂林涉嫌违纪违法及职务犯罪案", "黄桂林涉嫌违纪违法和职务犯罪案"]:
                replacements[placeholder_case] = case_name

        # 处理谈话人信息
        if "谈话人1姓名" in person_data and pd.notna(person_data["谈话人1姓名"]):
            interviewer1 = person_data["谈话人1姓名"]
            for placeholder in ["王五", "陈九"]:
                replacements[placeholder] = interviewer1

        if "谈话人2姓名" in person_data and pd.notna(person_data["谈话人2姓名"]):
            interviewer2 = person_data["谈话人2姓名"]
            for placeholder in ["赵六", "刘十"]:
                replacements[placeholder] = interviewer2

        # 处理安全员信息
        if "安全员姓名" in person_data and pd.notna(person_data["安全员姓名"]):
            safety_officer = person_data["安全员姓名"]
            for placeholder in ["孙七", "吴一"]:
                replacements[placeholder] = safety_officer

        # 处理组长信息
        if "组长姓名" in person_data and pd.notna(person_data["组长姓名"]):
            team_leader = person_data["组长姓名"]
            for placeholder in ["周八", "郑二"]:
                replacements[placeholder] = team_leader
        
        return replacements
    
    def process_document(self, template_path, output_path, person_data):
        """
        处理单个文档
        
        Args:
            template_path (str): 模板文档路径
            output_path (str): 输出文档路径
            person_data (dict): 人员数据
        """
        try:
            # 打开模板文档
            doc = Document(template_path)
            
            # 创建替换字典
            replacements = self.create_replacements_dict(person_data)
            
            # 替换段落中的文本
            for paragraph in doc.paragraphs:
                self.replace_text_in_paragraph(paragraph, replacements)
            
            # 替换表格中的文本
            for table in doc.tables:
                self.replace_text_in_table(table, replacements)
            
            # 保存文档
            doc.save(output_path)
            
        except Exception as e:
            raise Exception(f"处理文档失败: {str(e)}")
    
    def generate_documents(self, excel_path, selected_types, output_base_dir, progress_callback=None):
        """
        生成文书

        Args:
            excel_path (str): Excel文件路径
            selected_types (list): 选中的文书类型列表
            output_base_dir (str): 输出基础目录
            progress_callback (function): 进度回调函数

        Returns:
            dict: 生成结果
        """
        try:
            # 读取Excel数据
            df = self.read_excel_data(excel_path)

            results = {
                "success": True,
                "generated_files": [],
                "errors": [],
                "output_folders": []
            }

            total_tasks = len(df) * len(selected_types)
            current_task = 0

            # 获取当前日期
            current_date = datetime.now().strftime("%Y%m%d")

            # 为每个人员生成文书
            for index, row in df.iterrows():
                person_name = row.get("被谈话人姓名", f"人员{index+1}")

                # 创建个人文件夹：日期+姓名
                person_folder_name = f"{current_date}_{person_name}"
                person_folder_path = os.path.join(output_base_dir, person_folder_name)
                os.makedirs(person_folder_path, exist_ok=True)

                if person_folder_path not in results["output_folders"]:
                    results["output_folders"].append(person_folder_path)

                # 为每种选中的文书类型生成文档
                for doc_type in selected_types:
                    try:
                        if doc_type not in self.template_mapping:
                            results["errors"].append(f"未找到文书类型: {doc_type}")
                            continue

                        template_file = self.template_mapping[doc_type]
                        template_path = os.path.join(self.template_dir, template_file)

                        if not os.path.exists(template_path):
                            results["errors"].append(f"模板文件不存在: {template_path}")
                            continue

                        # 生成输出文件名（不包含时间戳，使用更简洁的命名）
                        output_filename = f"{doc_type}.docx"
                        output_path = os.path.join(person_folder_path, output_filename)

                        # 处理文档
                        self.process_document(template_path, output_path, row.to_dict())

                        results["generated_files"].append(output_path)

                        # 更新进度
                        current_task += 1
                        if progress_callback:
                            progress = (current_task / total_tasks) * 100
                            progress_callback(progress, f"已生成: {person_name} - {doc_type}")

                    except Exception as e:
                        error_msg = f"生成 {person_name} 的 {doc_type} 失败: {str(e)}"
                        results["errors"].append(error_msg)

            return results

        except Exception as e:
            return {
                "success": False,
                "generated_files": [],
                "errors": [f"生成文书失败: {str(e)}"],
                "output_folders": []
            }

def main():
    """测试函数"""
    processor = DocumentProcessor()
    
    # 测试Excel文件路径
    excel_path = "人员信息模板.xlsx"
    
    if os.path.exists(excel_path):
        selected_types = ["询问呈批表及通知书", "证人权利义务告知书"]
        
        def progress_callback(progress, message):
            print(f"进度: {progress:.1f}% - {message}")
        
        results = processor.generate_documents(excel_path, selected_types, progress_callback)
        
        print(f"\n生成结果:")
        print(f"成功: {results['success']}")
        print(f"生成文件数: {len(results['generated_files'])}")
        
        if results['generated_files']:
            print("生成的文件:")
            for file_path in results['generated_files']:
                print(f"  - {file_path}")
        
        if results['errors']:
            print("错误信息:")
            for error in results['errors']:
                print(f"  - {error}")
    else:
        print(f"Excel文件不存在: {excel_path}")

if __name__ == "__main__":
    main()
