# 文书生成工具

## 项目简介

这是一个基于Python开发的文书生成工具，具有谷歌风格的现代化界面。主要功能是通过上传Excel模板信息，自动将对应的值填写到Word文档中，生成对应的文书。

## 主要功能

1. **现代化GUI界面**：采用谷歌风格设计，操作简单直观
2. **Excel数据导入**：支持上传Excel文件，自动读取人员信息
3. **多种文书模板**：支持6种不同类型的文书生成
4. **批量处理**：可同时为多个人员生成多种文书
5. **进度显示**：实时显示生成进度和状态
6. **错误处理**：完善的错误提示和异常处理

## 支持的文书类型

- 询问呈批表及通知书
- 走读式谈话审批表格
- 封面
- 集体研判记录
- 证人权利义务告知书

## 系统要求

- Python 3.7+
- Windows 10/11（推荐）
- 至少500MB可用磁盘空间

## 安装步骤

### 1. 创建虚拟环境

```bash
python -m venv venv
```

### 2. 激活虚拟环境

Windows:
```bash
venv\Scripts\activate
```

### 3. 安装依赖包

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启动程序

```bash
python main.py
```

### 2. 准备Excel文件

- 下载Excel模板：点击界面中的"下载Excel模板"按钮
- 填写人员信息：按照模板格式填写需要生成文书的人员信息
- 保存Excel文件

### 3. 生成文书

1. 点击"选择Excel文件"按钮，上传填写好的Excel文件
2. 在预览区域查看导入的数据
3. 选择需要生成的文书类型
4. 点击"生成文书"按钮
5. 等待生成完成，程序会提示是否打开输出目录

## 文件结构

```
文书生成工具/
├── main.py                    # 主启动程序
├── document_generator_gui.py  # GUI界面模块
├── document_processor.py     # 文档处理模块
├── create_excel_template.py  # Excel模板创建工具
├── analyze_templates.py      # 文档模板分析工具
├── requirements.txt          # 依赖包列表
├── README.md                 # 项目说明文档
├── venv/                     # Python虚拟环境
├── 询问/                     # Word模板文件目录
│   ├── 询问呈批表及通知书.docx
│   ├── 整合后"走读式"谈话审批表格.docx
│   ├── 1.封面.docx
│   ├── 2.目录.docx
│   ├── 3.集体研判记录.docx
│   └── 证人权利义务告知书.docx
├── 生成的文书/               # 输出文件目录
└── 人员信息模板.xlsx         # Excel模板文件
```

## Excel模板字段说明

**注意：新版本使用分离的工作表结构，被谈话人信息和谈话人信息分别在不同的工作表中。**

### 被谈话人信息工作表
| 字段名称 | 说明 | 示例 |
|---------|------|------|
| 序号 | 人员序号 | 1 |
| 被谈话人姓名 | 被询问人姓名 | 张三 |
| 被谈话人身份证号 | 18位身份证号（自动提取性别和年龄） | 123456197801234567 |
| 被谈话人民族 | 民族 | 汉族 |
| 被谈话人学历 | 最高学历 | 本科 |
| 被谈话人政治面貌 | 党员身份 | 中共党员 |
| 被谈话人单位及职务 | 完整的工作单位和职务 | 某市检察院党组成员、副检察长 |
| 被谈话人联系电话 | 手机号码 | 13800138000 |
| 被谈话人家庭住址 | 详细家庭住址 | 某市某区某街道123号 |
| 被谈话人工作单位地址 | 工作单位地址 | 某市检察院 |
| 案件名称 | 相关案件名称 | 李某涉嫌违纪违法及职务犯罪案 |
| 填报部门 | 负责填报的部门 | 第四监督检查室 |
| 填报日期 | 日期格式：YYYY年MM月DD日 | 2025年7月31日 |
| 询问地点 | 询问地点 | 某市监委 |
| 谈话日期 | 日期格式：YYYY-MM-DD | 2025-08-02 |
| 谈话类型 | 下拉选择：1类谈话/2类谈话/3类谈话 | 3类谈话 |
| 谈话地点 | 具体谈话地点 | 某市监委谈话室 |
| 审批层次 | 下拉选择：省管正职/省管副职/省管其他干部/其他人员 | 其他人员 |
| 备注 | 其他信息 | 可选填写 |

### 谈话人信息工作表
| 字段名称 | 说明 | 示例 |
|---------|------|------|
| 序号 | 记录序号 | 1 |
| 关联被谈话人姓名 | 对应被谈话人的姓名（用于关联） | 张三 |
| 谈话人1姓名 | 主要谈话人姓名 | 王五 |
| 谈话人1单位及职务 | 主要谈话人职务 | 某市纪委监委第一监督检查室主任 |
| 谈话人2姓名 | 协助谈话人姓名 | 赵六 |
| 谈话人2单位及职务 | 协助谈话人职务 | 某市纪委监委第一监督检查室副主任 |
| 谈话人3姓名 | 第三谈话人姓名 | 钱七 |
| 谈话人3单位及职务 | 第三谈话人职务 | 某市纪委监委第一监督检查室干部 |
| 谈话人4姓名 | 第四谈话人姓名 | 李九 |
| 谈话人4单位及职务 | 第四谈话人职务 | 某县纪委监委第二监督检查室干部 |
| 安全员姓名 | 安全监督员姓名 | 孙七 |
| 安全员单位及职务 | 安全监督员职务 | 某市纪委监委安全保卫处干部 |
| 组长姓名 | 谈话组组长姓名 | 周八 |
| 组长单位及职务 | 组长职务 | 某市纪委监委副主任 |
| 谈话时间 | 具体谈话时间 | 2025年8月2日上午9:00 |
| 研判地点 | 集体研判地点 | 某市监委会议室 |
| 备注 | 其他信息 | 可选填写 |

### 填写说明工作表
包含详细的字段填写说明和示例，帮助用户正确填写各项信息。

## 使用流程

1. **下载模板**：点击"下载Excel模板"获取最新的分离式模板
2. **填写被谈话人信息**：在"被谈话人信息"工作表中填写基本信息
3. **填写谈话人信息**：在"谈话人信息"工作表中填写相关人员信息
4. **关联数据**：确保"关联被谈话人姓名"与被谈话人姓名完全一致
5. **选择选项**：使用下拉菜单选择谈话类型和审批层次
6. **生成文书**：上传Excel文件，选择文书类型，生成文档

## 常见问题

### Q: 程序启动失败怎么办？
A: 请检查：
1. Python版本是否为3.7+
2. 是否激活了虚拟环境
3. 是否安装了所有依赖包
4. 模板文件是否完整

### Q: 生成的文书内容不正确怎么办？
A: 请检查：
1. Excel文件格式是否正确
2. 字段名称是否与模板一致
3. 数据是否有缺失或格式错误

### Q: 如何添加新的文书模板？
A: 需要：
1. 将新的Word模板放入"询问"目录
2. 修改document_processor.py中的template_mapping
3. 更新字段映射关系

## 技术架构

- **GUI框架**：tkinter（Python标准库）
- **Excel处理**：openpyxl + pandas
- **Word处理**：python-docx
- **设计模式**：MVC架构
- **错误处理**：异常捕获和用户友好提示

## 开发团队

本项目由AI助手开发，采用模块化设计，代码结构清晰，易于维护和扩展。

## 版本历史

- v1.4.0 (2025-08-02)
  - 分离式工作表：被谈话人信息和谈话人信息使用独立工作表
  - 下拉选择菜单：谈话类型和审批层次支持下拉选择
  - 增强集体研判记录：完整的对话内容自动生成
  - 新增字段：谈话日期、谈话类型、谈话地点等
  - 完善填写说明：详细的字段说明和使用指南

- v1.3.0 (2025-08-02)
  - 完善字段映射：修复封面、集体研判记录、询问呈批表等所有文档的字段替换
  - 扩展谈话人支持：支持最多4名谈话人信息
  - 增强界面显示：窗口自动最大化，优化表格列宽
  - 新增字段：谈话时间、研判地点、审批层次等
  - 智能审批层次选择：根据Excel数据自动勾选对应选项

- v1.2.0 (2025-08-02)
  - 智能身份证号解析：自动从身份证号提取性别和年龄
  - 优化文件组织：按日期+姓名创建个人文件夹
  - 保持原文档格式：生成时不改变字体、下划线等格式
  - 改进用户体验：添加保存路径选择，默认文件名设置
  - 修复模板下载问题

- v1.1.0 (2025-08-02)
  - 美化界面，支持高DPI显示
  - 新增多人员信息支持（被谈话人、谈话人、安全员、组长）
  - 优化文件名处理，移除特殊字符
  - 改进用户体验和界面布局

- v1.0.0 (2025-08-02)
  - 初始版本发布
  - 支持5种文书类型
  - 完整的GUI界面
  - 批量处理功能

## 许可证

本项目仅供内部使用，请勿外传。

## 联系方式

如有问题或建议，请联系开发团队。
